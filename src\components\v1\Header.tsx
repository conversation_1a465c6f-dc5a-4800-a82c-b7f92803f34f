'use client';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import React, { useEffect, useState } from 'react';

import { images } from '@/constants';

const defaultNavItems = [
  { name: 'Home', path: '/v1/home', normal: images.Home, active: images.HomeActive },
  { name: 'People', path: '/v1/people', normal: images.People, active: images.PeopleActive },
  { name: 'Teaching', path: '/v1/teaching', normal: images.Teaching, active: images.TeachingActive },
  { name: 'Publications', path: '/v1/publications', normal: images.Publications, active: images.PublicationsActive },
  { name: 'Lodestar', path: '/v1/lodestar', normal: images.Lodestar, active: images.LodestarActive },
  { name: 'UsefulLinks', path: '/v1/useful-links', normal: images.UsefulLinks, active: images.UsefulLinksActive },
];

const usefulLinksNavItems = [
  { name: 'Home', path: '/v1/home', normal: images.Home, active: images.HomeActive },
  { name: 'Up', path: '', normal: images.Up, active: images.UpActive }, // 空路径,通过点击事件处理
  { name: 'People', path: '/v1/people', normal: images.People, active: images.PeopleActive },
  { name: 'Teaching', path: '/v1/teaching', normal: images.Teaching, active: images.TeachingActive },
  { name: 'Publications', path: '/v1/publications', normal: images.Publications, active: images.PublicationsActive },
  { name: 'Lodestar', path: '/v1/lodestar', normal: images.Lodestar, active: images.LodestarActive },
];
const resumeNavItems = [
  { name: 'Home', path: '/v1/home', normal: images.Home, active: images.HomeActive },
  { name: 'Up', path: '', normal: images.Up, active: images.UpActive },
  { name: 'Research', path: '/v1/research', normal: images.Research, active: images.ResearchActive },
  { name: 'Schedule', path: '/v1/schedule', normal: images.Schedule, active: images.ScheduleActive },
  { name: 'Teaching', path: '/v1/teaching', normal: images.Teaching, active: images.TeachingActive },
  { name: 'Publications', path: '/v1/publications', normal: images.Publications, active: images.PublicationsActive },
  { name: 'Lodestar', path: '/v1/lodestar', normal: images.Lodestar, active: images.LodestarActive },
  { name: 'UsefulLinks', path: '/v1/useful-links', normal: images.UsefulLinks, active: images.UsefulLinksActive },
];
const researchNavItems = [
  { name: 'Home', path: '/v1/home', normal: images.Home, active: images.HomeActive },
  { name: 'Research', path: '/v1/research', normal: images.Research, active: images.ResearchActive },
  { name: 'People', path: '/v1/people', normal: images.People, active: images.PeopleActive },
  { name: 'Schedule', path: '/v1/schedule', normal: images.Schedule, active: images.ScheduleActive },
  { name: 'Teaching', path: '/v1/teaching', normal: images.Teaching, active: images.TeachingActive },
  { name: 'Publications', path: '/v1/publications', normal: images.Publications, active: images.PublicationsActive },
  { name: 'Lodestar', path: '/v1/lodestar', normal: images.Lodestar, active: images.LodestarActive },
  { name: 'UsefulLinks', path: '/v1/useful-links', normal: images.UsefulLinks, active: images.UsefulLinksActive },
];
const V1Header = () => {
  const pathname = usePathname();
  const router = useRouter();
  // 判断是否在useful-links路由下
  const isUsefulLinksRoute = pathname.startsWith('/v1/useful-links/');
  const isResumeRoute = pathname.startsWith('/v1/people/') && pathname !== '/v1/people';
  const isResearchOrScheduleRoute = pathname === '/v1/research' || pathname === '/v1/schedule';
  // 选择使用哪个导航项数组
  let navItems = defaultNavItems;
  if (isUsefulLinksRoute) {
    navItems = usefulLinksNavItems;
  } else if (isResumeRoute) {
    navItems = resumeNavItems;
  } else if (isResearchOrScheduleRoute) {
    navItems = researchNavItems;
  }

  // 处理Up按钮点击
  const handleUpClick = () => {
    router.back();
  };

  return (
    <header className="w-full">
      <div className="container mx-auto px-8">
        <Link href="/v1/lodestar" className="flex-shrink-0 mt-2 flex justify-center">
          <Image src={images.Logo} alt="Logo" width={550} height={60} className="object-contain" />
        </Link>
        <div className="flex items-center h-20">
          <nav className="flex-1 flex items-center justify-center space-x-8">
            {navItems.map((item) => {
              if (item.name === 'Up') {
                return (
                  <button key={item.name} onClick={handleUpClick} className="relative group">
                    <Image
                      src={item.normal}
                      alt={item.name}
                      width={140}
                      height={60}
                      className="transition-opacity duration-200 hover:opacity-80"
                    />
                  </button>
                );
              }
              return (
                <Link key={item.name} href={item.path} className="relative group">
                  <Image
                    src={pathname === item.path ? item.active : item.normal}
                    alt={item.name}
                    width={140}
                    height={60}
                    className="transition-opacity duration-200 hover:opacity-80"
                  />
                </Link>
              );
            })}
          </nav>
        </div>
        <div className="py-2 flex items-center justify-center">
          <Image src={images.Amaizrul} alt="Amaizrul" className="object-contain" />
        </div>
      </div>
    </header>
  );
};

export default V1Header;
