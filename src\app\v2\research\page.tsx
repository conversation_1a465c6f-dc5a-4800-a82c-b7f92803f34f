import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { researchData } from '@/db/v2/research';

export default function V2ResearchPage() {
  return (
    <BaseLayout headerType="header2" contentType="content2" showSidebar={false}>
      <h2>Research topics</h2>
      <p style={{ width: '720px', textAlign: 'justify' }}>
        {researchData.topics.map((topic) => (
          <span key={topic.id}>
            <b>
              <span className="text-white font-bold">{topic.title}</span>
            </b>
            <br />
            {topic.content.split('\n\n').map((paragraph, index) => (
              <span key={index}>
                {paragraph}
                {index < topic.content.split('\n\n').length - 1 && (
                  <>
                    <br />
                    <br />
                  </>
                )}
              </span>
            ))}
            <br />
            <br />
          </span>
        ))}
      </p>
    </BaseLayout>
  );
}
