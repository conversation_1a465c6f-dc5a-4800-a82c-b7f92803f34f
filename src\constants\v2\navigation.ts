// 导航常量 - v2版本 (基于原始 yangtze_web 样式)
export interface NavItem {
  id: string;
  label: string;
  href: string;
  isActive?: boolean;
}

export const navigationItems: NavItem[] = [
  {
    id: 'home',
    label: 'Home',
    href: '/v2',
  },
  {
    id: 'research',
    label: 'Research',
    href: '/v2/research',
  },
  {
    id: 'publications',
    label: 'Publications',
    href: '/v2/publications',
  },
  {
    id: 'group',
    label: 'Group',
    href: '/v2/group',
  },
  {
    id: 'teaching',
    label: 'Teaching',
    href: '/v2/teaching',
  },
  {
    id: 'software',
    label: 'Software',
    href: '/v2/software',
  },
  {
    id: 'links',
    label: 'Links',
    href: '/v2/links',
  },
];

// 保持向后兼容
export const v2NavItems = navigationItems.map((item) => ({
  name: item.label,
  path: item.href,
  icon: item.id,
}));

export const v2ThemeConfig = {
  colors: {
    primary: '#2563eb',
    secondary: '#64748b',
    accent: '#06b6d4',
    background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    surface: 'rgba(255, 255, 255, 0.95)',
    text: {
      primary: '#1e293b',
      secondary: '#64748b',
    },
  },
  fonts: {
    primary: "'Inter', -apple-system, BlinkMacSystemFont, sans-serif",
    heading: "'Poppins', sans-serif",
  },
  spacing: {
    container: '1200px',
    section: '80px',
  },
};
