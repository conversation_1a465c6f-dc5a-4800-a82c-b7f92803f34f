export interface Presentation {
  id: number;
  topic: string;
  speaker: string;
  date: string;
}

export interface YearData {
  year: number;
  presentations: Presentation[];
}

export const presentations: YearData[] = [
  {
    year: 2002,
    presentations: [
      {
        id: 1,
        topic: 'Simplified tight-bonding model for the optical properties of the carbon nanotubes',
        speaker: '<PERSON><PERSON>',
        date: 'Jul 30, 2002 (<PERSON><PERSON>)',
      },
      {
        id: 2,
        topic: 'PRISM algorithm',
        speaker: 'Mr. <PERSON>',
        date: 'Aug 7, 2002 (Wed)',
      },
    ],
  },
];
