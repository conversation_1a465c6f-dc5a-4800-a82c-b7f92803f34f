'use client';

import Image from 'next/image';
import Link from 'next/link';
import { useState } from 'react';

export default function ReqdpostPage() {
  const [formData, setFormData] = useState({
    subject: '',
    from: '',
    comments: '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: 处理表单提交
    console.log('Form submitted:', formData);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <div
      className="min-h-screen bg-[#adadad]"
      style={{
        backgroundImage: 'url("/maizbk.jpg")',
        backgroundRepeat: 'repeat',
      }}
    >
      <div className="grid grid-rows-[auto_1fr_auto] min-h-screen w-4/5 mx-auto  text-[#336699]">
        {/* Header Section */}
        <div className="p-4">
          <h1 className="text-4xl text-[#CC9900] mb-4">Suggestion</h1>
          <div className="flex gap-2 mb-4">
            <Link href="/lodestar" className="text-[#669999] hover:text-[#996633]">
              [ Up |
            </Link>
            <Link href="/lodestar/reqdpost" className="text-[#669999] hover:text-[#996633]">
              Post ]
            </Link>
          </div>
          <div className="text-center">
            <Image src="/gif/amaizrul.gif" alt="separator" width={600} height={10} />
          </div>
        </div>

        {/* Main Content */}
        <div className="p-8">
          <h2 className="text-2xl text-[#003399] mb-6">Post Article</h2>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="font-bold block mb-2">Subject:</label>
              <input
                type="text"
                name="subject"
                value={formData.subject}
                onChange={handleChange}
                className="w-full max-w-lg p-2 border"
                maxLength={256}
              />
            </div>

            <div>
              <label className="font-bold block mb-2">From:</label>
              <input
                type="text"
                name="from"
                value={formData.from}
                onChange={handleChange}
                className="w-full max-w-lg p-2 border"
                maxLength={256}
              />
            </div>

            <div>
              <label className="font-bold block mb-2">Comments:</label>
              <textarea
                name="comments"
                value={formData.comments}
                onChange={handleChange}
                rows={10}
                className="w-full max-w-lg p-2 border"
              />
            </div>

            <div className="flex gap-4 mt-6">
              <button type="submit" className="px-4 py-2 bg-gray-200 hover:bg-gray-300">
                Post Article
              </button>
              <button
                type="reset"
                className="px-4 py-2 bg-gray-200 hover:bg-gray-300"
                onClick={() =>
                  setFormData({
                    subject: '',
                    from: '',
                    comments: '',
                  })
                }
              >
                Clear Form
              </button>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="p-4">
          <div className="text-center mb-4">
            <Image src="/gif/amaizrul.gif" alt="separator" width={600} height={10} />
          </div>
          <h5 className="text-[#003399] text-center">Last changed: August 07, 2002</h5>
        </div>
      </div>
    </div>
  );
}
