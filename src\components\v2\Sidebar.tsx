'use client';

import Image from 'next/image';
import Link from 'next/link';

interface NewsItem {
  title: string;
  link?: string;
  image?: string;
  description?: string;
}

interface PastEvent {
  title: string;
  link?: string;
  image?: string;
  description?: string;
}

interface SidebarProps {
  news: NewsItem[];
  pastEvents: PastEvent[];
}

const Sidebar = ({ news, pastEvents }: SidebarProps) => {
  return (
    <div className="border-l border-[#FBB917] float-right w-[210px]" style={{ paddingLeft: '15px' }}>
      {/* News Section */}
      <h3 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2">
        News
      </h3>
      <div className="block text-[1.1em] m-0 p-2">
        {news.map((item, index) => (
          <div key={index} className="mb-4">
            <span className="font-bold text-white">• </span>
            {item.link ? (
              <Link href={item.link} className="font-bold text-[#FAAC58] hover:text-[#FF7F00]">
                {item.title}
                {item.image && (
                  <div className="mt-2">
                    <Image
                      src={item.image}
                      alt={item.title}
                      width={180}
                      height={100}
                      className="max-w-[180px]"
                      unoptimized
                    />
                  </div>
                )}
              </Link>
            ) : (
              <span className="font-bold">{item.title}</span>
            )}
            {item.description && (
              <Link href={item.link || '#'} className="block text-right text-[#FAAC58] hover:text-[#FF7F00]">
                {item.description}
              </Link>
            )}
            <br />
          </div>
        ))}
      </div>

      {/* Past Events Section */}
      <h3 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2">
        Past events
      </h3>
      <div className="block text-[1.1em] m-0 p-2">
        {pastEvents.map((item, index) => (
          <div key={index} className="mb-4">
            <span className="font-bold text-white">• </span>
            {item.link ? (
              <Link href={item.link} className="font-bold text-[#FAAC58] hover:text-[#FF7F00]">
                {item.title}
                {item.image && (
                  <div className="mt-2">
                    <Image
                      src={item.image}
                      alt={item.title}
                      width={180}
                      height={100}
                      className="max-w-[180px]"
                      unoptimized
                    />
                  </div>
                )}
              </Link>
            ) : (
              <span className="font-bold">{item.title}</span>
            )}
            {item.description && (
              <Link href={item.link || '#'} className="block text-right text-[#FAAC58] hover:text-[#FF7F00]">
                {item.description}
              </Link>
            )}
            <br />
          </div>
        ))}
      </div>
    </div>
  );
};

export default Sidebar;
