// import Image from 'next/image';

// import { images } from '@/constants';
import { cn } from '@/lib/utils';
import V1FooterNav from './FooterNav';

const V1Footer = () => {
  return (
    <footer className={cn('w-full')}>
      {/* 添加导航组件 */}
      <V1FooterNav />
      {/* 页脚内容 */}
      {/* <div className="py-2 flex items-center justify-center">
        <Image src={images.Amaizrul} alt="Amaizrul" className="object-contain" />
      </div> */}
      <div className="container mx-auto px-4 py-6">
        <div className="flex flex-col items-center space-y-1 font-bold text-[13.28px] text-[#103399] font-[Arial,Helvetica,sans-serif]">
          <div className="text-center">
            <p>© 2005-2025 Prof. GuanHua Chen Research Group, the University of Hong Kong.</p>
          </div>
          <div className="text-center">
            <p>
              For problems or questions regarding this web contact{' '}
              <a href="mailto:<EMAIL>" className={cn('text-[#669999] underline transition-colors')}>
                <EMAIL>
              </a>
            </p>
          </div>
          <div className="text-center">
            <p>Last updated: March 08, 2005</p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default V1Footer;
