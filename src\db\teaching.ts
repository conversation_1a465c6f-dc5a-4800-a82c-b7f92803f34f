export interface LectureNote {
  title: string;
  url: string;
  format: string;
  lastUpdate?: string;
}

export interface CourseSection {
  title: string;
  notes: (
    | LectureNote
    | {
        title: string;
        items: Array<{
          title: string;
          url: string;
          format?: string;
        }>;
      }
  )[];
}

export const courseSections: CourseSection[] = [
  {
    title: 'Year 2',
    notes: [
      {
        title: 'Intermediate Physical Chemistry (CHEM2503)',
        url: 'http://yangtze.hku.hk/lecture/chem2503-06.ppt',
        format: 'Powerpoint format .ppt',
      },
    ],
  },
  {
    title: 'Year 3',
    notes: [
      {
        title: 'Advanced Physical Chemistry',
        url: 'http://yangtze.hku.hk/lecture/chem3513-0809.ppt',
        format: 'Powerpoint format .ppt',
        lastUpdate: '14 Mar, 2010',
      },
      {
        title: 'Computational Chemistry',
        url: 'http://yangtze.hku.hk/lecture/chem3506-6109_1003.ppt',
        format: 'Powerpoint format .ppt',
        lastUpdate: '14 Mar, 2010',
      },
      {
        title: 'Electronic Spectroscopy',
        url: 'http://yangtze.hku.hk/lecture/chem3505_sep06.ppt',
        format: 'Powerpoint format .ppt',
      },
      {
        title: 'Electronic Spectroscopy (assignment)',
        url: 'http://yangtze.hku.hk/lecture/assignment.rar',
        format: 'rar file',
      },
    ],
  },
  {
    title: 'Postgraduate Course',
    notes: [
      {
        title: 'Research Techniques in Chemistry',
        url: 'http://yangtze.hku.hk/lecture/comput06-07.ppt',
        format: 'Powerpoint format .ppt',
      },
      {
        title: 'Additional Materials',
        items: [
          {
            title: 'Course Work',
            url: 'http://yangtze.hku.hk/lecture/chemtech.doc',
          },
          {
            title: 'Download Molecule',
            url: 'http://yangtze.hku.hk/lecture/1DEF.ent',
          },
        ],
      },
    ],
  },
  {
    title: 'M.Sc Course',
    notes: [
      {
        title: 'Computational Modeling of Macromolecular Systems',
        url: 'http://yangtze.hku.hk/lecture/comput-ms-04-05.ppt',
        format: 'Powerpoint format .ppt',
      },
      {
        title: 'Additional Materials',
        items: [
          {
            title: 'Download Molecule',
            url: 'http://yangtze.hku.hk/lecture/1C51.ent',
          },
        ],
      },
    ],
  },
  {
    title: 'Lab Manuals',
    notes: [
      {
        title: 'Computational Chemistry',
        items: [
          {
            title: 'Using LODESTAR to Calculate the Absorption Spectrum of Ethylene',
            url: 'http://yangtze.hku.hk/lecture/ComputationalChemistry_lodestar.doc',
          },
          {
            title: 'Structure file ethylene.xyz',
            url: 'http://yangtze.hku.hk/lecture/ethylene.xyz',
          },
        ],
      },
    ],
  },
];
