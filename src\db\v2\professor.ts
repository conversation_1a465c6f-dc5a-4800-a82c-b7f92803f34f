export interface Professor {
  name: string;
  title: string;
  education: string[];
  positions: string[];
  email: string[];
  image: string;
  researchInterests: string;
}

export const professorData: Professor = {
  name: 'Prof<PERSON> <PERSON><PERSON>',
  title: 'Chair Professor, Department of Chemistry',
  education: ['<PERSON><PERSON>Sc. (Fudan, 86)', 'Ph.D. (Caltech, 92)'],
  positions: [
    'Chair Professor, Department of Chemistry<br>The University of Hong Kong',
    'Managinng Director, Hong Kong Quantum AI Lab<br>(AIR@InnoHK, Program of ITC)',
    'Co-Convenor, Strategic Research Theme on <i>Computation and Information</i><br>The University of Hong Kong',
    'Principal Coordinator, Area of Excellence on <i>Theory, Simulation and Modelling of Emerging Electronics</i>',
  ],
  email: ['<EMAIL>', '<EMAIL>'],
  image: '/v2/img/upload/ghc.jpg',
  researchInterests:
    'My group focuses on developing first-principles quantum mechanical methods to model the real materials and devices. For this, we develop first-principles methods for open quantum system (OQS), and employ machine learning to improve the accuracy of first-principles quantum mechanical methods so that quantitatively precise prediction of material properties is possible.',
};
