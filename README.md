## Features

- ⚡️ Next.js 15 (App Router)
- ⚛️ React 19
- ⛑ TypeScript
- 📏 ESLint 9 — To find and fix problems in your code
- 💖 Prettier — Code Formatter for consistent style
- 🐶 <PERSON><PERSON> — For running scripts before committing
- 🚓 Commitlint — To make sure your commit messages follow the convention
- 🖌 Renovate — To keep your dependencies up to date
- 🚫 lint-staged — Run ESLint and Prettier against staged Git files
- 👷 PR Workflow — Run Type Check & Linters on Pull Requests
- ⚙️ EditorConfig - Consistent coding styles across editors and IDEs
- 🗂 Path Mapping — Import components or images using the `@` prefix
- 🔐 CSP — Content Security Policy for enhanced security (default minimal policy)
- 🧳 T3 Env — Type-safe environment variables
- 🪧 Redirects — Easily add redirects to your application

## Quick Start

```bash
yarn
yarn dev
yarn build
```

```bash
src/
├── api/                    # API 请求封装
│   ├── auth.ts            # 认证相关 API
│   ├── user.ts            # 用户相关 API
│   └── request.ts         # Axios 请求配置
├── components/            # 组件
│   ├── common/            # 通用组件
│   │   ├── Button/
│   │   ├── Input/
│   │   └── Table/
│   └── business/          # 业务组件
│       ├── UserList/
│       └── DataChart/
├── config/               # 配置文件
│   ├── constants.ts      # 常量配置
│   └── settings.ts       # 应用设置
├── hooks/               # 自定义 Hooks
│   ├── useAuth.ts
│   └── useUser.ts
├── layouts/             # 布局组件
│   ├── MainLayout/
│   └── AuthLayout/
├── pages/              # 页面组件
│   ├── auth/
│   │   ├── login/
│   │   └── register/
│   └── dashboard/
├── store/              # 状态管理
│   ├── auth/
│   └── user/
├── styles/             # 样式文件
│   ├── global.css
│   └── variables.css
├── types/              # TypeScript 类型定义
│   ├── api.ts
│   ├── user.ts
│   └── common.ts
└── utils/             # 工具函数
    ├── auth.ts
    ├── format.ts
    └── validate.ts
```
