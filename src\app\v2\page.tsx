import Image from 'next/image';
import Link from 'next/link';

import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { homePageData } from '@/db/v2/home';

export default function V2HomePage() {
  const {
    professor,
    academicPositions,
    researchSynopsis,
    awards,
    professionalServices,
    supervisionStats,
    representativePublications,
    news,
    pastEvents,
    visitorCount,
  } = homePageData;

  return (
    <BaseLayout
      headerType="header1"
      contentType="content1"
      showSidebar={true}
      news={news}
      pastEvents={pastEvents}
      visitorCount={visitorCount}
    >
      <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
        Welcome to the homepage of G.H. Chen Group
      </h2>
      <p className="m-0 p-2 relative">
        <br />
        <Image
          src={professor.photo}
          width={225}
          height={207}
          alt="Prof. <PERSON><PERSON>"
          className="border border-[#FBB917] float-right ml-2 mb-2"
        />
        <b>
          <Link href="/v2/group#ProfGuanhuaChen" className="text-[#FAAC58] hover:text-[#FF7F00]">
            {professor.name}
          </Link>
        </b>
        <br />
        {professor.education}
        <br />
        <ul className="my-4">
          {professor.positions.map((position, index) => (
            <li key={index} dangerouslySetInnerHTML={{ __html: position }} />
          ))}
        </ul>
        <br />
        Email:{' '}
        {professor.email.map((email, index) => (
          <span key={index}>
            <a href={`mailto:${email}`} className="text-[#FAAC58] hover:text-[#FF7F00]">
              {email}
            </a>
            {index < professor.email.length - 1 ? ' or ' : ''}
          </span>
        ))}
        <br />
        <br />
      </p>

      <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
        Academic Positions
      </h2>
      <div className="p-2">
        <ul>
          {academicPositions.map((position, index) => (
            <li key={index}>
              {position.position}, {position.period}
            </li>
          ))}
        </ul>
        <br />
      </div>

      <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
        Research synopsis
      </h2>
      <p className="m-0 p-2 relative">
        {researchSynopsis}
        <br />
        <br />
      </p>

      <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
        Awards
      </h2>
      <div className="p-2">
        <ul>
          {awards.map((award, index) => (
            <li key={index}>
              {award.title} {award.year && `(${award.year})`}
            </li>
          ))}
        </ul>
        <br />
      </div>

      <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
        Professional Services
      </h2>
      <div className="p-2">
        <ul>
          {professionalServices.map((service, index) => (
            <li key={index}>
              {service.service}
              {service.period && ` (${service.period})`}
            </li>
          ))}
        </ul>
        <br />
      </div>

      <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
        Postgraduate Supervision
      </h2>
      <div className="p-2">
        <table>
          <tbody>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;&nbsp;&bull;&nbsp;</td>
              <td>Ph.D./M.Phil. candidates under supervision:</td>
              <td className="text-right">{supervisionStats.currentPhDMPhil}</td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;&nbsp;&bull;&nbsp;</td>
              <td>Ph.D. awarded under my supervision:</td>
              <td className="text-right">{supervisionStats.awardedPhD}</td>
            </tr>
            <tr>
              <td>&nbsp;&nbsp;&nbsp;&nbsp;&bull;&nbsp;</td>
              <td>M.Phil. awarded under my supervision:&nbsp;&nbsp;</td>
              <td className="text-right">{supervisionStats.awardedMPhil}</td>
            </tr>
          </tbody>
        </table>
        <br />
      </div>

      <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
        Representative Publication
      </h2>
      <div className="p-2">
        <ol className="text-justify">
          {representativePublications.map((pub) => (
            <li key={pub.id}>
              "{pub.title}", {pub.authors}, <b className="text-white font-bold">{pub.journal}</b> {pub.volume},{' '}
              {pub.pages} ({pub.year})
            </li>
          ))}
        </ol>
      </div>
    </BaseLayout>
  );
}
