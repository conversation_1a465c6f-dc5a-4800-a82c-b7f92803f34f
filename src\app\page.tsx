'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

import { useVersion } from '@/lib/version-context';

export default function HomePage() {
  const router = useRouter();
  const { setVersion } = useVersion();

  useEffect(() => {
    // 默认设置为V2版本并跳转
    setVersion('v2');
    // 使用 replace 而不是 push，避免在历史记录中留下根路径
    router.replace('/v2/home');
  }, [router, setVersion]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">加载中...</p>
        {/* <div className="mt-4 text-sm text-gray-500">
          <a href="/v1/home" className="text-blue-600 hover:underline">
            点击这里访问经典版本
          </a>
        </div> */}
      </div>
    </div>
  );
}
