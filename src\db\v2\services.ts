export interface ProfessionalService {
  title: string;
  period?: string;
}

export const professionalServices: ProfessionalService[] = [
  {
    title: 'Vice-President, Hong Kong Institution of Science',
    period: '(2013-2015)',
  },
  {
    title: 'Board member for Journal of Physical Chemistry and Journal of Physical Chemistry Letters',
    period: '(06/2017-02/2021)',
  },
];

export interface SupervisionStats {
  label: string;
  count: number;
}

export const supervisionStats: SupervisionStats[] = [
  {
    label: 'Ph.D./M.Phil. candidates under supervision:',
    count: 15,
  },
  {
    label: 'Ph.D. awarded under my supervision:',
    count: 31,
  },
  {
    label: 'M.Phil. awarded under my supervision:',
    count: 2,
  },
];
