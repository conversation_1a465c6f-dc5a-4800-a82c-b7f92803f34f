import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { GroupPhotos } from '@/components/v1/people/GroupPhotos';
import MemberList from '@/components/v1/people/MemberList';
import { MemberProfiles } from '@/components/v1/people/MemberProfiles';
import { currentMembers, formerMembers } from '@/db/people';

export default function PeoplePage() {
  return (
    <V1BaseLayout>
      <div className="max-w-6xl mx-auto p-4 space-y-8">
        {/* 介绍文字 */}
        <div className="text-center text-[#003366] space-y-2">
          <p>This page contains contact information for all the members of our research group.</p>
          <p>From here you can send e-mail to members, or visit their personal home pages.</p>
        </div>

        {/* 成员列表导航 */}
        <MemberList />

        {/* 团队照片 */}
        <GroupPhotos />

        {/* 成员详细信息 */}
        <MemberProfiles currentMembers={currentMembers} formerMembers={formerMembers} />
      </div>
    </V1BaseLayout>
  );
}
