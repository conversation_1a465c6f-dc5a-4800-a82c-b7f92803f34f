import { NextResponse } from 'next/server';

import type { NextRequest } from 'next/server';

// 定义不需要显示Header和Footer的路由
const noLayoutRoutes = ['/lodestar/download', '/lodestar/reqdpost', '/v1/lodestar/download', '/v1/lodestar/reqdpost'];

// 定义v2版本路由（不需要v1的Header和Footer）
const v2Routes = ['/v2'];

export function middleware(request: NextRequest) {
  const requestHeaders = new Headers(request.headers);
  requestHeaders.set('x-pathname', request.nextUrl.pathname);

  // 检查当前路径是否需要隐藏布局
  const shouldHideLayout =
    noLayoutRoutes.includes(request.nextUrl.pathname) ||
    v2Routes.some((route) => request.nextUrl.pathname.startsWith(route));
  requestHeaders.set('x-hide-layout', shouldHideLayout ? '1' : '0');

  return NextResponse.next({
    request: {
      headers: requestHeaders,
    },
  });
}

export const config = {
  matcher: '/:path*',
};
