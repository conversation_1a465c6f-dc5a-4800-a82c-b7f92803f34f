'use client';

import Footer from '../Footer';
import V2Header from '../Header';
import Sidebar from '../Sidebar';

interface NewsItem {
  title: string;
  link?: string;
  image?: string;
  description?: string;
}

interface PastEvent {
  title: string;
  link?: string;
  image?: string;
  description?: string;
}

interface BaseLayoutProps {
  children: React.ReactNode;
  headerType?: 'header1' | 'header2' | 'header3' | 'header4' | 'header5' | 'header6' | 'header7';
  contentType?: 'content1' | 'content2' | 'content3' | 'content4' | 'content5' | 'content6' | 'content7';
  showSidebar?: boolean;
  news?: NewsItem[];
  pastEvents?: PastEvent[];
  visitorCount?: number;
}

export default function BaseLayout({
  children,
  headerType = 'header1',
  contentType = 'content1',
  showSidebar = true,
  news = [],
  pastEvents = [],
  visitorCount = 71041,
}: BaseLayoutProps) {
  return (
    <div
      className="bg-[#303030] text-[#D8D8D8] m-0 p-0 text-center min-h-screen"
      style={{
        backgroundImage: "url('/v2/img/background/body.jpg')",
        backgroundRepeat: 'repeat-x',
        backgroundPosition: '0 0',
        font: '100.1%/1 Verdana, Arial, Helvetica, sans-serif',
      }}
    >
      {/* Container */}
      <div
        className="mx-auto text-left w-[760px] relative"
        style={{
          fontSize: '0.7em',
          lineHeight: '2',
        }}
      >
        {/* Header */}
        <V2Header variant={headerType} />

        {/* Main content area - using original layout structure */}
        <div className="relative min-h-[400px]">
          {/* Content */}
          <div
            className={`float-left bg-no-repeat bg-[#303030] min-h-[400px] ${showSidebar ? 'w-[550px]' : 'w-[760px]'}`}
            style={{
              backgroundImage: `url('/v2/img/background/${contentType}.jpg')`,
            }}
          >
            {children}
          </div>

          {/* Sidebar */}
          {showSidebar && <Sidebar news={news} pastEvents={pastEvents} />}

          {/* Clear float */}
          <div className="clear-both"></div>
        </div>
      </div>
      {/* Footer - positioned right after content */}
      <Footer visitorCount={visitorCount} />
    </div>
  );
}
