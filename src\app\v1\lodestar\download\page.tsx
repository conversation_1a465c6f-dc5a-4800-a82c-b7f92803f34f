'use client';

import { useState } from 'react';

// 邮箱后缀验证数组
const EMAIL_EXTENSIONS = [
  '.com',
  '.net',
  '.org',
  '.biz',
  '.coop',
  '.info',
  '.museum',
  '.name',
  '.pro',
  '.edu',
  '.gov',
  '.int',
  '.mil',
  '.ac',
  '.ad',
  '.ae',
  '.af',
  '.ag',
  '.ai',
  '.al',
  '.am',
  '.an',
  '.ao',
  '.aq',
  '.ar',
  '.as',
  '.at',
  '.au',
  '.aw',
  '.az',
  '.ba',
  '.bb',
  '.bd',
  '.be',
  '.bf',
  '.bg',
  '.bh',
  '.bi',
  '.bj',
  '.bm',
  '.bn',
  '.bo',
  '.br',
  '.bs',
  '.bt',
  '.bv',
  '.bw',
  '.by',
  '.bz',
  '.ca',
  '.cc',
  '.cd',
  '.cf',
  '.cg',
  '.ch',
  '.ci',
  '.ck',
  '.cl',
  '.cm',
  '.cn',
  '.co',
  '.cr',
  '.cu',
  '.cv',
  '.cx',
  '.cy',
  '.cz',
  '.de',
  '.dj',
  '.dk',
  '.dm',
  '.do',
  '.dz',
  '.ec',
  '.ee',
  '.eg',
  '.eh',
  '.er',
  '.es',
  '.et',
  '.fi',
  '.fj',
  '.fk',
  '.fm',
  '.fo',
  '.fr',
  '.ga',
  '.gd',
  '.ge',
  '.gf',
  '.gg',
  '.gh',
  '.gi',
  '.gl',
  '.gm',
  '.gn',
  '.gp',
  '.gq',
  '.gr',
  '.gs',
  '.gt',
  '.gu',
  '.gv',
  '.gy',
  '.hk',
  '.hm',
  '.hn',
  '.hr',
  '.ht',
  '.hu',
  '.id',
  '.ie',
  '.il',
  '.im',
  '.in',
  '.io',
  '.iq',
  '.ir',
  '.is',
  '.it',
  '.je',
  '.jm',
  '.jo',
  '.jp',
  '.ke',
  '.kg',
  '.kh',
  '.ki',
  '.km',
  '.kn',
  '.kp',
  '.kr',
  '.kw',
  '.ky',
  '.kz',
  '.la',
  '.lb',
  '.lc',
  '.li',
  '.lk',
  '.lr',
  '.ls',
  '.lt',
  '.lu',
  '.lv',
  '.ly',
  '.ma',
  '.mc',
  '.md',
  '.mg',
  '.mh',
  '.mk',
  '.ml',
  '.mm',
  '.mn',
  '.mo',
  '.mp',
  '.mq',
  '.mr',
  '.ms',
  '.mt',
  '.mu',
  '.mv',
  '.mw',
  '.mx',
  '.my',
  '.mz',
  '.na',
  '.nc',
  '.ne',
  '.nf',
  '.ng',
  '.ni',
  '.nl',
  '.no',
  '.np',
  '.nr',
  '.nu',
  '.nz',
  '.om',
  '.pa',
  '.pe',
  '.pf',
  '.pg',
  '.ph',
  '.pk',
  '.pl',
  '.pm',
  '.pn',
  '.pr',
  '.ps',
  '.pt',
  '.pw',
  '.py',
  '.qa',
  '.re',
  '.ro',
  '.rw',
  '.ru',
  '.sa',
  '.sb',
  '.sc',
  '.sd',
  '.se',
  '.sg',
  '.sh',
  '.si',
  '.sj',
  '.sk',
  '.sl',
  '.sm',
  '.sn',
  '.so',
  '.sr',
  '.st',
  '.sv',
  '.sy',
  '.sz',
  '.tc',
  '.td',
  '.tf',
  '.tg',
  '.th',
  '.tj',
  '.tk',
  '.tm',
  '.tn',
  '.to',
  '.tp',
  '.tr',
  '.tt',
  '.tv',
  '.tw',
  '.tz',
  '.ua',
  '.ug',
  '.uk',
  '.um',
  '.us',
  '.uy',
  '.uz',
  '.va',
  '.vc',
  '.ve',
  '.vg',
  '.vi',
  '.vn',
  '.vu',
  '.ws',
  '.wf',
  '.ye',
  '.yt',
  '.yu',
  '.za',
  '.zm',
  '.zw',
];

export default function DownloadPage() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    title: '',
    organization: '',
    country: '',
  });

  const checkMailId = (email: string) => {
    const dot = email.lastIndexOf('.');
    const ext = email.substring(dot);
    const at = email.indexOf('@');

    if (dot > 5 && at > 1) {
      for (let i = 0; i < EMAIL_EXTENSIONS.length; i++) {
        if (ext === EMAIL_EXTENSIONS[i]) {
          return true;
        }
      }
      alert('Your email ' + email + ' is not correct');
      return false;
    } else {
      alert('Your email ' + email + ' is not correct');
      return false;
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    if (!checkMailId(formData.email)) {
      return;
    }

    // TODO: 处理表单提交
    console.log('Form submitted:', formData);
  };

  const handleReset = () => {
    setFormData({
      name: '',
      email: '',
      title: '',
      organization: '',
      country: '',
    });
  };

  return (
    <div
      style={{
        fontFamily: 'Arial,Verdana,san-serif',
        margin: '0px',
        backgroundColor: '#adadad',
        minHeight: '100vh',
        display: 'flex',
        flexDirection: 'column',
      }}
    >
      <table
        style={{
          width: '80%',
          margin: '0 auto',
          height: '100vh',
          padding: '0',
          borderSpacing: '0',
          border: '1px solid',
          backgroundColor: '#a89a89',
        }}
      >
        <tbody>
          <tr style={{ height: '10%' }}>
            <td
              style={{
                height: '80px',
                textAlign: 'center',
                backgroundImage: "url('/maizbk.jpg')",
                backgroundRepeat: 'repeat',
                backgroundPosition: 'center',
              }}
            >
              <br />
              <div style={{ fontSize: '24pt', color: '#223356', fontWeight: 'bold' }}>LODESTAR</div>
              <br />
              <br />
            </td>
          </tr>

          <tr style={{ height: '70%' }}>
            <td
              style={{
                textAlign: 'left',
                verticalAlign: 'top',
                fontSize: '14px',
                border: 'dashed 0px',
              }}
            >
              <br />
              <form onSubmit={handleSubmit} style={{ margin: '0' }}>
                <table style={{ margin: '0 auto', padding: '4px', borderSpacing: '0', border: '0' }}>
                  <tbody>
                    <tr>
                      <td>
                        <br />
                        Name *:
                      </td>
                      <td>
                        <br />
                        <input
                          type="text"
                          name="name"
                          size={27}
                          value={formData.name}
                          onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                          required
                        />
                      </td>
                    </tr>
                    <tr>
                      <td>Email *:</td>
                      <td>
                        <input
                          type="text"
                          name="from"
                          size={27}
                          value={formData.email}
                          onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                          required
                        />
                      </td>
                    </tr>
                    <tr>
                      <td>Title:</td>
                      <td>
                        <input
                          type="text"
                          name="title"
                          size={27}
                          value={formData.title}
                          onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                        />
                      </td>
                    </tr>
                    <tr>
                      <td>Organization *:</td>
                      <td>
                        <input
                          type="text"
                          name="organ"
                          size={27}
                          value={formData.organization}
                          onChange={(e) => setFormData({ ...formData, organization: e.target.value })}
                          required
                        />
                      </td>
                    </tr>
                    <tr>
                      <td>Country:</td>
                      <td>
                        <input
                          type="text"
                          name="country"
                          size={27}
                          value={formData.country}
                          onChange={(e) => setFormData({ ...formData, country: e.target.value })}
                        />
                      </td>
                    </tr>
                    <tr>
                      <td colSpan={2} style={{ textAlign: 'right' }}>
                        <input
                          type="submit"
                          value="Submit"
                          style={{
                            padding: '2px 6px',
                            border: '2px outset buttonface',
                            borderRadius: '3px',
                            backgroundColor: 'buttonface',
                            cursor: 'pointer',
                            marginRight: '4px',
                          }}
                        />
                        <input
                          type="button"
                          value="Clear"
                          style={{
                            padding: '2px 6px',
                            border: '2px outset buttonface',
                            borderRadius: '3px',
                            backgroundColor: 'buttonface',
                            cursor: 'pointer',
                          }}
                          onClick={handleReset}
                        />
                      </td>
                    </tr>
                  </tbody>
                </table>
              </form>
              <br />
            </td>
          </tr>

          <tr style={{ height: '15%' }}>
            <td
              style={{
                textAlign: 'center',
                backgroundImage: "url('/maizbk.jpg')",
                backgroundRepeat: 'repeat',
                backgroundPosition: 'center',
              }}
            >
              <br />
              <br />
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  );
}
