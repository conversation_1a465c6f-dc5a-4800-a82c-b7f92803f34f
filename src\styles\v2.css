/* V2 样式 - 基于原始 yangtze_web/home/<USER>/css/style.css */

/* 基础样式 */
body {
  background: #303030 url('/v2/img/background/body.jpg') repeat-x;
  background-position: 0 0;
  color: #d8d8d8;
  font:
    100.1%/1 Verdana,
    Arial,
    Helvetica,
    sans-serif;
  margin: 0;
  padding: 0;
  text-align: center;
}

/* 链接样式 */
a {
  background: transparent;
  color: #faac58;
  text-decoration: none;
}

a:hover {
  color: #ff7f00;
}

/* 粗体文本 */
b {
  background: transparent;
  color: #ffffff;
  font-family: Arial;
  font-weight: bold;
}

/* 引用块 */
blockquote {
  background: #b45f04;
  border-left: 2px solid #8a0808;
  color: #ffffff;
  font-style: italic;
  margin: 0 0 0 20px;
  padding: 0 0 0 10px;
}

/* 图片样式 */
.imgright {
  border: 1px solid #fbb917;
  float: right;
  margin: 10px 0px 10px 10px;
}

.imgleft {
  border: 1px solid #fbb917;
  float: left;
  margin: 10px 10px 10px 0px;
}

.imgleftnoborder {
  float: left;
  margin: 10px 10px 10px 0px;
}

/* 容器样式 */
.container {
  font-size: 0.7em;
  line-height: 2;
  margin: 0 auto;
  text-align: left;
  width: 760px;
}

.container:after {
  clear: both;
  content: '.';
  display: block;
  height: 0;
  visibility: hidden;
}

/* 标题样式 */
h1 {
  color: #671c04;
  font-size: 3.5em;
  font-weight: 700;
  letter-spacing: -4px;
  line-height: 75px;
  margin: 0;
  padding: 25px 0px 0;
}

h2 {
  border-left: 8px solid #fbb917;
  color: #ffe87c;
  font-size: 1.6em;
  font-weight: 400;
  line-height: 1.75;
  margin: 0;
  padding: 0 10px;
  clear: both;
}

h3 {
  border-left: 8px solid #fbb917;
  color: #ffe87c;
  font-size: 1.6em;
  font-weight: 400;
  line-height: 1.75;
  margin: 0;
  padding: 0 10px;
}

h4 {
  color: #ffffff;
  font-size: 1.2em;
  font-style: italic;
}

h5 {
  clear: both;
  border-top: 2px solid #fbb917;
}

/* 段落样式 */
p {
  margin: 0;
  padding: 10px;
  position: relative;
}

/* 侧边栏样式 */
.sidebar {
  border-left: 1px solid #fbb917;
  padding: 0 0 0 15px;
  float: right;
  width: 210px;
}

.sidebar p {
  background: transparent;
  display: block;
  font-size: 1.1em;
  margin: 0;
  padding: 10px;
}

.sidebar p a.more {
  display: block;
  text-align: right;
}

.sidebar ul {
  margin: 0px;
  padding: 0px;
  width: 180px;
}

.sidebar ul li {
  list-style: none;
  padding: 0px;
  border-bottom: 1px solid #fbb917;
}

.sidebar ul li a {
  color: #fff9d2;
  display: block;
  padding: 5px;
  text-decoration: none;
}

.sidebar ul li a:hover {
  color: #ff7f00;
}

/* 页脚样式 */
.footer {
  background: #303030 url('/v2/img/background/footer.bmp') repeat-x;
  height: 70px;
  clear: both;
  margin: 0 auto;
  padding: 0;
  font: 100.1%/1;
}

.footer p {
  color: #bdbdbd;
  font-size: 0.7em;
  line-height: 2em;
  margin: 0;
  padding: 17px;
  text-align: center;
  position: relative;
  top: 10px;
}
