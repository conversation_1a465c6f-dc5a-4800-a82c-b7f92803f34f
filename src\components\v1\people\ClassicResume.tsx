import Link from 'next/link';

import type { ResumeData } from '@/db/people';

interface ClassicResumeProps {
  data: ResumeData;
  name: string;
}

export default function ClassicResume({ data, name }: ClassicResumeProps) {
  return (
    <div className="min-h-screen p-4">
      <div className="max-w-4xl mx-auto">
        {/* 标题 */}
        <h1 className="text-center text-[#000080] text-3xl font-bold mb-8">Resume</h1>

        {/* 姓名 */}
        <div className="mb-6">
          <h2 className="text-[#000080] text-xl font-bold">Name</h2>
          <p className="text-[#000099] font-bold">{name}</p>
        </div>

        {/* 出生日期 */}
        {data.dateOfBirth && (
          <div className="mb-6">
            <h2 className="text-[#000080] text-xl font-bold">Date of Birth:</h2>
            <p className="text-[#000099] font-bold">{data.dateOfBirth}</p>
          </div>
        )}

        {/* 研究兴趣 */}
        {data.researchInterestsDetail && (
          <div className="mb-6">
            <h2 className="text-[#000080] text-xl font-bold">Research Interests:</h2>
            <p className="text-[#000099] font-bold">{data.researchInterestsDetail}</p>
          </div>
        )}

        {/* 教育背景 */}
        {data.education && data.education.length > 0 && (
          <div className="mb-6">
            <h2 className="text-[#000080] text-xl font-bold">Education:</h2>
            {data.education.map((edu, index) => (
              <div key={index} className="mb-4">
                <p className="text-[#000099] font-bold">
                  {edu.degree} {edu.date} {edu.institution}
                </p>
                {edu.advisor && <p className="text-[#000099]">Advisor: {edu.advisor}</p>}
                {edu.thesis && <p className="text-[#000099]">Thesis: {edu.thesis}</p>}
              </div>
            ))}
          </div>
        )}

        {/* 工作经历 */}
        {data.professionalExperience && data.professionalExperience.length > 0 && (
          <div className="mb-6">
            <h2 className="text-[#000080] text-xl font-bold">Professional Experience:</h2>
            {data.professionalExperience.map((exp, index) => (
              <div key={index} className="mb-2">
                <p className="text-[#000099] font-bold">
                  {exp.period} {exp.position}
                </p>
              </div>
            ))}
          </div>
        )}

        {/* 返回链接 */}
        <div className="mt-8 text-center">
          <Link href="/v1/people" className="text-[#669999] hover:text-[#996633] underline">
            Back to People
          </Link>
        </div>
      </div>
    </div>
  );
}
