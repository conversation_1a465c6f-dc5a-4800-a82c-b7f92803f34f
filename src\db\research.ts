export interface ResearchItem {
  id: number;
  title: string;
  content: string;
  indented?: boolean; // 用于控制是否缩进
}

export const researchInterests: ResearchItem[] = [
  {
    id: 1,
    title: 'Dynamics of Very Large Molecular Systems',
    content: `We are interested in simulating the dynamic processes of large complex systems, such as polymer
    aggregates or films, biological molecules and nano-structures. To simulate the electronic dynamics of
    these systems, we have developed a linear-scaling method for electronic ground and excited states,
    the localized-density-matrix (LDM) method. It has been employed to determine the absorption spectra
    of large polyacetylene oligomers, PPV aggregates, carbon nanotubes and light-harvesting systems.
    The largest system that has been calculated is a polyacetylene oligomer that contains 30,000 carbon atoms,
    and the PPP Hamiltonian was employed in the calculation. The LDM method has been so far implemented at
    semiempirical CNDO/S, INDO/S, PM3 and AM1, and the time-dependent density functional theory (TDDFT).
    Efforts are under the way to calculate other dynamic properties, for example, emission spectrum,
    circular dichroism spectrum, conductivity and NMR signals.`,
    indented: true,
  },
  {
    id: 2,
    title: 'Quantum Chemistry Simulation of Open Systems',
    content: `Quantum dissipation is a subject of wide spread interests in many fields of physics, chemistry
    and materials science. Various quantum dissipation theories (QDTs) have been developed to investigate
    the dynamic properties of open systems, for instance, the Bloch-Redfield theory, Fokker-Planck equation
    and Lindblad semi-group formalism. The key physical quantity in all these theories is the reduced system
    density matrix. The computational costs of these theories are very expensive, and the calculations are
    thus limited to the small model systems. We propose a new formalism to simulate the electronic dynamics
    of open systems with the ultimate objective to investigate the complex open systems from the first-principles...`,
    indented: true,
  },
  {
    id: 3,
    title: 'Computer-Aided Drug Design',
    content: `We have developed a neural network based software to construct the quantitative structure-activity
    relationship (QSAR) using the existing experimental data. It has been used to search for drug candidates
    of aldose reductase inhibitors (ARIs). Combined with Cerius2, Autodock and QM/MM program, it provides
    an efficient and powerful tool to design drug candidates on computer.`,
    indented: true,
  },
  {
    id: 4,
    title: 'Quantum Computing, Quantum Information and Decoherence',
    content: `Quantum computing and communication have been subjects of wide interests in recent years. A key
    difficulty for quantum computing and communication is the quantum decoherence. In order to investigate
    the dissipation of quantum states, we employ several model systems subject to a harmonic oscillator bath.
    These models can be solved exactly, and thus their dissipations can be examined in details. Conditions
    for dissipation free or few dissipation have been established.`,
    indented: true,
  },
];
