import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { researchInterests } from '@/db/research';

export default function ResearchPage() {
  return (
    <V1BaseLayout>
      <div className="min-h-screen  text-[#336699] p-4">
        <div className="max-w-4xl mx-auto">
          {/* 标题 */}
          <div className="text-center mb-12">
            <h1 className="text-5xl italic text-[#800000]">Research Interests</h1>
          </div>

          {/* 研究方向列表 */}
          {researchInterests.map((item) => (
            <div key={item.id} className="mb-8">
              <h2 className="text-xl font-bold italic text-[#000080] mb-4">
                {item.id}.&nbsp;&nbsp;&nbsp;{item.title}
              </h2>
              <div className={`text-justify ${item.indented ? 'pl-8' : ''}`}>
                <p>{item.content}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </V1BaseLayout>
  );
}

// 设置页面元数据
export const metadata = {
  title: 'Research Interests - Research Group',
  description: 'Research interests and areas of focus for our research group',
};
