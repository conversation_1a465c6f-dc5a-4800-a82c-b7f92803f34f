import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { courseSections } from '@/db/teaching';

export default function TeachingPage() {
  return (
    <V1BaseLayout>
      <div className="max-w-4xl mx-auto py-4 px-4">
        <h1 className="text-3xl font-bold mb-8 text-center font-['trebuchet_ms,_arial,_helvetica'] text-[#336699] italic">
          Lecture Notes for Physical Chemistry
        </h1>

        {courseSections.map((section, index) => (
          <div key={index} className="mb-6">
            <h2 className="text-xl text-[#336699] font-['trebuchet_ms'] mb-2">{section.title}</h2>
            <ol className="list-decimal list-inside">
              {section.notes.map((note, noteIndex) => (
                <li key={noteIndex} className="mb-2 font-['trebuchet_ms']">
                  {'url' in note ? (
                    <>
                      <a href={note.url} className="text-[#669999] hover:underline underline">
                        {note.title}
                      </a>
                      {note.format && ` (${note.format})`}
                      {note.lastUpdate && (
                        <span className="text-red-600 font-bold ml-2">updated on {note.lastUpdate}</span>
                      )}
                    </>
                  ) : (
                    <>
                      {note.title}:
                      <div className="ml-8">
                        {note.items.map((item, itemIndex) => (
                          <div key={itemIndex}>
                            <a href={item.url} className="text-[#669999] underline  hover:underline">
                              {item.title}
                            </a>
                            {item.format && ` (${item.format})`}
                          </div>
                        ))}
                      </div>
                    </>
                  )}
                </li>
              ))}
            </ol>
          </div>
        ))}
      </div>
    </V1BaseLayout>
  );
}
