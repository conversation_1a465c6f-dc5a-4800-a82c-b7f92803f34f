import Image from 'next/image';

import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { groupData } from '@/db/v2/group';

// 创建专门的GroupSidebar组件
function GroupSidebar() {
  return (
    <div className="border-l border-[#FBB917] float-right w-[210px]" style={{ paddingLeft: '15px' }}>
      <br />
      {/* Current members Section */}
      <b className="text-white">Current members</b>
      <ul className="m-0 p-0 w-[180px]">
        {groupData.currentMembers.map((member) => (
          <li key={member.id} className="list-none p-0 border-b border-[#FBB917]">
            <a
              href={`#${member.id}`}
              className="text-[#FAAC58] hover:text-[#FF7F00] block py-1"
              style={{ textDecoration: 'none' }}
            >
              {member.name}
            </a>
          </li>
        ))}
      </ul>
      <br />
      {/* Former members Section */}
      <b className="text-white">Former members</b>
      <ul className="m-0 p-0 w-[180px]">
        {groupData.formerMembers?.map((member) => (
          <li key={member.id} className="list-none p-0 border-b border-[#FBB917]">
            <a
              href={`#${member.id}`}
              className="text-[#FAAC58] hover:text-[#FF7F00] block py-1"
              style={{ textDecoration: 'none' }}
            >
              {member.name}
            </a>
          </li>
        ))}
      </ul>
      <br />
      <br />
    </div>
  );
}

export default function V2GroupPage() {
  return (
    <>
      <BaseLayout headerType="header4" contentType="content4" showSidebar={false}>
        {/* 主内容区域 */}
        <div
          className="float-left bg-no-repeat bg-[#303030] min-h-[400px] w-[550px]"
          style={{ backgroundImage: "url('/v2/img/background/content4.jpg')" }}
        >
          <a id="top"></a>
          <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
            Group photos
          </h2>
          <div className="m-0 p-2 text-center">
            {groupData.photos.map((photo) => (
              <span key={photo.id}>
                <Image
                  className="border border-[#FBB917] mx-auto block"
                  src={photo.src}
                  width={490}
                  height={366}
                  alt={photo.description || photo.date}
                />
                <br />
                {photo.date}
                <br />
                <br />
              </span>
            ))}
          </div>

          <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
            Current members
          </h2>
          {groupData.currentMembers.map((member) => (
            <div key={member.id} className="m-0 p-2 relative">
              <a id={member.id}></a>
              {member.photo && (
                <Image
                  className={`border border-[#FBB917] ${member.isPI ? 'float-right' : 'float-left'}`}
                  src={member.photo}
                  width={member.isPI ? 300 : 230}
                  height={member.isPI ? 300 : 230}
                  alt={member.name}
                  style={{ margin: member.isPI ? '10px 0px 10px 10px' : '10px 10px 10px 0px' }}
                />
              )}
              <h4 className="text-white text-[1.2em] italic font-normal">{member.name}</h4>
              {member.cv && (
                <>
                  <a href={member.cv} className="text-[#FAAC58] hover:text-[#FF7F00]">
                    CURRICULUM VITAE
                  </a>
                  <br />
                </>
              )}
              {member.education}
              <br />
              <br />
              {member.position}
              <br />
              {member.detailedInfo?.positions?.map((pos, index) => (
                <span key={index}>
                  {pos}
                  <br />
                </span>
              ))}
              <br />
              {member.detailedInfo?.honoraryPositions && (
                <>
                  <b className="text-white font-bold">Honorary positions:</b>
                  <br />
                  {member.detailedInfo.honoraryPositions.map((pos, index) => (
                    <span key={index}>
                      {pos}
                      <br />
                    </span>
                  ))}
                  <br />
                </>
              )}
              {member.detailedInfo?.prizesAndAwards && (
                <>
                  <b className="text-white font-bold">Prizes and awards:</b>
                  <br />
                  {member.detailedInfo.prizesAndAwards.map((award, index) => (
                    <span key={index}>
                      {award}
                      <br />
                    </span>
                  ))}
                  <br />
                </>
              )}
              {member.detailedInfo?.professionalServices && (
                <>
                  <b className="text-white font-bold">Professional Services / Standing:</b>
                  <br />
                  {member.detailedInfo.professionalServices.map((service, index) => (
                    <span key={index}>
                      {service}
                      <br />
                    </span>
                  ))}
                  <br />
                </>
              )}
              {member.detailedInfo?.editorialBoards && (
                <>
                  <b className="text-white font-bold">Editorial board membership:</b>
                  <br />
                  {member.detailedInfo.editorialBoards.map((board, index) => (
                    <span key={index}>
                      {board}
                      <br />
                    </span>
                  ))}
                  <br />
                </>
              )}
              {member.detailedInfo?.scientificPedigree && (
                <>
                  <b className="text-white font-bold">Scientific pedigree:</b>
                  <br />
                  <i>
                    {member.detailedInfo.scientificPedigree.map((pedigree, index) => (
                      <span key={index}>
                        * {pedigree}
                        <br />
                      </span>
                    ))}
                  </i>
                  <br />
                </>
              )}
              {!member.detailedInfo && (
                <>
                  <b className="text-white font-bold">Position: </b>
                  {member.position}
                  <br />
                </>
              )}
              {member.researchInterests && (
                <>
                  <b className="text-white font-bold">Research interests: </b>
                  {member.researchInterests}
                  <br />
                </>
              )}
              {member.hobbies && (
                <>
                  <b className="text-white font-bold">Hobbies: </b>
                  {member.hobbies}
                  <br />
                </>
              )}
              {member.office && (
                <>
                  <b className="text-white font-bold">Office: </b>
                  {member.office}
                  <br />
                </>
              )}
              {member.email && (
                <>
                  <b className="text-white font-bold">Email: </b>
                  <a href={`mailto:${member.email}`} className="text-[#FAAC58] hover:text-[#FF7F00]">
                    {member.email}
                  </a>
                  <br />
                </>
              )}
              <br />
              <div className="text-right m-0 mb-2">
                <a href="#top" className="text-[#FAAC58] hover:text-[#FF7F00]">
                  top &uarr;
                </a>
              </div>
              <h5 className="clear-both border-t-2 border-[#FBB917] m-0 pt-2"> </h5>
            </div>
          ))}

          <br />
          <h2 className="border-l-8 border-[#FBB917] text-[#FFE87C] text-[1.6em] font-normal leading-[1.75] m-0 pl-2 clear-both">
            Former members
          </h2>
          {groupData.formerMembers?.map((member) => (
            <div key={member.id} className="m-0 p-2 relative">
              <a id={member.id}></a>
              {member.photo && (
                <Image
                  className="border border-[#FBB917] float-left"
                  src={member.photo}
                  width={230}
                  height={230}
                  alt={member.name}
                  style={{ margin: '10px 10px 10px 0px' }}
                />
              )}
              <h4 className="text-white text-[1.2em] italic font-normal">{member.name}</h4>
              {member.education && (
                <>
                  {member.education}
                  <br />
                  <br />
                </>
              )}
              {member.workedAs && (
                <>
                  <b className="text-white font-bold">Worked as: </b>
                  {member.workedAs}
                  <br />
                </>
              )}
              {member.position && (
                <>
                  <b className="text-white font-bold">Current position: </b>
                  {member.position}
                  <br />
                </>
              )}
              {member.researchInterests && (
                <>
                  <b className="text-white font-bold">Research interests: </b>
                  {member.researchInterests}
                  <br />
                </>
              )}
              {member.hobbies && (
                <>
                  <b className="text-white font-bold">Hobbies: </b>
                  {member.hobbies}
                  <br />
                </>
              )}
              {member.office && (
                <>
                  <b className="text-white font-bold">Office: </b>
                  {member.office}
                  <br />
                </>
              )}
              {member.email && (
                <>
                  <b className="text-white font-bold">Email: </b>
                  <a href={`mailto:${member.email}`} className="text-[#FAAC58] hover:text-[#FF7F00]">
                    {member.email}
                  </a>
                  <br />
                </>
              )}
              <br />
              <div className="text-right m-0 mb-2">
                <a href="#top" className="text-[#FAAC58] hover:text-[#FF7F00]">
                  top &uarr;
                </a>
              </div>
              <h5 className="clear-both border-t-2 border-[#FBB917] m-0 pt-2"> </h5>
            </div>
          ))}
        </div>

        {/* 自定义侧边栏 */}
        <GroupSidebar />

        {/* Clear float */}
        <div className="clear-both"></div>
      </BaseLayout>
    </>
  );
}
