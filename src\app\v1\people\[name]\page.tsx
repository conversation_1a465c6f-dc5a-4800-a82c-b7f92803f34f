import { notFound } from 'next/navigation';

import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import ClassicResume from '@/components/v1/people/ClassicResume';
import { currentMembers, formerMembers } from '@/db/people';

interface PageProps {
  params: {
    name: string;
  };
}

export default async function MemberResumePage({ params }: PageProps) {
  const resolvedParams = await params;
  const decodedName = decodeURIComponent(resolvedParams.name);

  // 查找成员
  const member = [...currentMembers, ...formerMembers].find((m) => {
    const formattedName = m.name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    return formattedName === decodedName;
  });

  // 如果没有找到成员或者成员没有简历数据，返回404
  if (!member?.resume) {
    notFound();
  }

  return (
    <V1BaseLayout>
      <div className="container mx-auto px-4">
        <ClassicResume data={member.resume} name={member.name} />
      </div>
    </V1BaseLayout>
  );
}

// 生成静态路径
export async function generateStaticParams() {
  const allMembers = [...currentMembers, ...formerMembers];

  return allMembers
    .filter((member) => member.isResume && member.resume)
    .map((member) => ({
      name: member.name
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-|-$/g, ''),
    }));
}

// 设置元数据
export async function generateMetadata({ params }: PageProps) {
  const decodedName = decodeURIComponent(params.name);

  const member = [...currentMembers, ...formerMembers].find((m) => {
    const formattedName = m.name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');

    return formattedName === decodedName;
  });

  if (!member) {
    return {
      title: 'Member Not Found',
    };
  }

  return {
    title: `${member.name}'s Resume - Research Group`,
    description: `Professional resume of ${member.name}`,
  };
}
