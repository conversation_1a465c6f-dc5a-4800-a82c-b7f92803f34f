/**
 * CSPs that we're not adding (as it can change from project to project):
 * frame-src, connect-src, script-src, child-src, style-src, worker-src, font-src, media-src, and img-src
 */
const ContentSecurityPolicy = `
  object-src 'none';
  base-uri 'self';
  frame-ancestors 'self';
  manifest-src 'self';
  report-to default;
`;

// For more information, check https://nextjs.org/docs/app/api-reference/config/next-config-js/headers
const securityHeaders = [
  {
    key: 'X-DNS-Prefetch-Control',
    value: 'on',
  },
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload',
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block',
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff',
  },
  {
    key: 'Referrer-Policy',
    value: 'no-referrer-when-downgrade',
  },
  {
    key: 'Permissions-Policy',
    value: `accelerometer=(), camera=(), gyroscope=(), microphone=(), usb=()`,
  },
  {
    key: 'Content-Security-Policy',
    value: ContentSecurityPolicy.replace(/\n/g, ''),
  },
];

const { redirects } = require('./redirects');

const nextConfig = {
  poweredByHeader: false,
  output: 'standalone',
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders,
      },
    ];
  },
  async redirects() {
    return redirects;
  },
  async rewrites() {
    return [
      // v1路由重写（保持向后兼容）
      // {
      //   source: '/home',
      //   destination: '/v1/home',
      // },
      {
        source: '/people/:path*',
        destination: '/v1/people/:path*',
      },
      {
        source: '/teaching/:path*',
        destination: '/v1/teaching/:path*',
      },
      {
        source: '/publications/:path*',
        destination: '/v1/publications/:path*',
      },
      {
        source: '/lodestar',
        destination: '/v1/lodestar',
      },
      {
        source: '/lodestar/:path*',
        destination: '/v1/lodestar/:path*',
      },
      {
        source: '/useful-links/:path*',
        destination: '/v1/useful-links/:path*',
      },
      {
        source: '/research/:path*',
        destination: '/v1/research/:path*',
      },
      {
        source: '/schedule/:path*',
        destination: '/v1/schedule/:path*',
      },
    ];
  },
  reactStrictMode: true,
};

module.exports = nextConfig; // 使用 CommonJS 导出
