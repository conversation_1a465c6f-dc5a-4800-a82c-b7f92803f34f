import { MemberProfile } from './MemberProfile';

import type { Member } from '@/db/people';

interface MemberProfilesProps {
  currentMembers: Member[];
  formerMembers: Member[];
}

export function MemberProfiles({ currentMembers, formerMembers }: MemberProfilesProps) {
  return (
    <div className="space-y-12">
      {/* 分隔线 */}
      <hr className="border-t border-[#999966]" />

      {/* 当前成员 */}
      <div className="space-y-16">
        {currentMembers.map((member) => (
          <div key={member.name} id={member.name.toLowerCase().replace(/\s+/g, '-')} className="pb-8">
            <MemberProfile member={member} type="current" />
          </div>
        ))}
      </div>

      {/* 分隔线 */}
      <hr className="border-t border-[#999966]" />

      {/* 前成员 */}
      <div className="space-y-16">
        {formerMembers.map((member) => (
          <div key={member.name} id={member.name.toLowerCase().replace(/\s+/g, '-')} className="pb-8">
            <MemberProfile member={member} type="former" />
          </div>
        ))}
      </div>
    </div>
  );
}
