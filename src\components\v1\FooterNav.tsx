'use client';

import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';

import { images } from '@/constants';

// 定义路径类型
type NavPath =
  | '/v1/home'
  | '/v1/people'
  | '/v1/teaching'
  | '/v1/publications'
  | '/v1/lodestar'
  | '/v1/useful-links'
  | '/v1/research'
  | '/v1/schedule';

// 定义不同场景的导航顺序
const defaultNavOrder: NavPath[] = [
  '/v1/home',
  '/v1/people',
  '/v1/teaching',
  '/v1/publications',
  '/v1/lodestar',
  '/v1/useful-links',
];

const usefulLinksNavOrder: NavPath[] = ['/v1/home', '/v1/people', '/v1/teaching', '/v1/publications', '/v1/lodestar'];

const resumeNavOrder: NavPath[] = [
  '/v1/home',
  '/v1/research',
  '/v1/schedule',
  '/v1/teaching',
  '/v1/publications',
  '/v1/lodestar',
  '/v1/useful-links',
];

const researchNavOrder: NavPath[] = [
  '/v1/home',
  '/v1/research',
  '/v1/people',
  '/v1/schedule',
  '/v1/teaching',
  '/v1/publications',
  '/v1/lodestar',
  '/v1/useful-links',
];

const V1FooterNav = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [currentIndex, setCurrentIndex] = useState(-1);
  const [navigationOrder, setNavigationOrder] = useState<NavPath[]>(defaultNavOrder);

  // 判断当前路由场景并设置导航顺序
  useEffect(() => {
    const isUsefulLinksRoute = pathname.startsWith('/v1/useful-links/');
    const isResumeRoute = pathname.startsWith('/v1/people/') && pathname !== '/v1/people';
    const isResearchOrScheduleRoute = pathname === '/v1/research' || pathname === '/v1/schedule';

    if (isUsefulLinksRoute) {
      setNavigationOrder(usefulLinksNavOrder);
    } else if (isResumeRoute) {
      setNavigationOrder(resumeNavOrder);
    } else if (isResearchOrScheduleRoute) {
      setNavigationOrder(researchNavOrder);
    } else {
      setNavigationOrder(defaultNavOrder);
    }
  }, [pathname]);

  useEffect(() => {
    const index = navigationOrder.indexOf(pathname as NavPath);
    setCurrentIndex(index);
  }, [pathname, navigationOrder]);

  const handleBack = () => {
    router.back();
  };

  const handleUp = () => {
    const pathParts = pathname.split('/');
    if (pathParts.length > 2) {
      const upperPath = pathParts.slice(0, -1).join('/');
      router.push(upperPath);
    }
  };

  const handleHome = () => {
    router.push('/v1/home');
  };

  const handleNext = () => {
    if (currentIndex >= 0 && currentIndex < navigationOrder.length - 1) {
      const nextPath = navigationOrder[currentIndex + 1];
      if (nextPath) {
        router.push(nextPath);
      }
    }
  };

  const showBack = currentIndex > 0;
  const showNext = currentIndex < navigationOrder.length - 1;
  const showUp = pathname.split('/').length > 2;

  return (
    <div className="flex flex-col items-center space-y-4">
      {/* 分隔线 */}
      <div className="w-full flex justify-center">
        <Image src={images.Amaizrul} alt="separator" width={600} height={10} className="object-contain" />
      </div>

      {/* 导航按钮 */}
      <div className="flex justify-center items-center space-x-4">
        {showBack && (
          <button onClick={handleBack} className="relative group">
            <Image src={images.Back} alt="Back" width={100} height={20} className="block group-hover:hidden" />
            <Image src={images.BackActive} alt="Back" width={100} height={20} className="hidden group-hover:block" />
          </button>
        )}

        {showUp && (
          <button onClick={handleUp} className="relative group">
            <Image src={images.Bup} alt="Up" width={100} height={20} className="block group-hover:hidden" />
            <Image src={images.BupActive} alt="Up" width={100} height={20} className="hidden group-hover:block" />
          </button>
        )}

        <button onClick={handleHome} className="relative group">
          <Image src={images.BHome} alt="Home" width={100} height={20} className="block group-hover:hidden" />
          <Image src={images.BHomeActive} alt="Home" width={100} height={20} className="hidden group-hover:block" />
        </button>

        {showNext && (
          <button onClick={handleNext} className="relative group">
            <Image src={images.Next} alt="Next" width={100} height={20} className="block group-hover:hidden" />
            <Image src={images.NextActive} alt="Next" width={100} height={20} className="hidden group-hover:block" />
          </button>
        )}
      </div>
    </div>
  );
};

export default V1FooterNav;
