import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { publicationsData } from '@/db/publications';

export default function PublicationsPage() {
  return (
    <V1BaseLayout>
      <div className="max-w-6xl mx-auto py-8 px-4">
        <h1 className="text-3xl font-bold text-center text-[#800000] mb-8 font-['trebuchet_ms,_arial,_helvetica']">
          Publications of Prof. G.H. Chen's Research Group
        </h1>

        <div className="space-y-4">
          {publicationsData.publications.map((pub) => (
            <div key={pub.id} className="text-[#336699] font-bold font-['trebuchet_ms,_arial,_helvetica']">
              <p>
                {pub.id}. "{pub.title}",{' '}
                {pub.authors.map((author, idx) => (
                  <span key={idx}>
                    {author}
                    {pub.isCorrespondingAuthor?.[idx] && '*'}
                    {idx < pub.authors.length - 1 ? ', ' : ''}
                  </span>
                ))}
                ,
                {pub.pdfLink ? (
                  <a
                    href={pub.pdfLink}
                    className="text-[#669999] hover:text-[#999966] underline visited:text-[#999966] active:text-[#CC9900]"
                  >
                    {pub.journal} ({pub.year})
                  </a>
                ) : (
                  <span>
                    {pub.journal} ({pub.year})
                  </span>
                )}
                .
              </p>
            </div>
          ))}
        </div>

        <hr className="my-8" />

        <h2 className="text-xl text-[#336699]  font-bold mb-4 font-['trebuchet_ms,_arial,_helvetica']">
          Paper in preparation...
        </h2>

        <div className="space-y-4">
          {publicationsData.preparedPublications.map((pub) => (
            <div key={pub.id} className="text-[#336699] font-bold font-['trebuchet_ms,_arial,_helvetica']">
              <p>
                {pub.id}.{' '}
                <a
                  href={pub.pdfLink}
                  className="text-[#669999] hover:text-[#999966] underline visited:text-[#999966] active:text-[#CC9900]"
                >
                  "{pub.title}"
                </a>
                ,{' '}
                {pub.authors.map((author, idx) => (
                  <span key={idx}>
                    {author}
                    {idx < pub.authors.length - 1 ? ', ' : ''}
                  </span>
                ))}
              </p>
            </div>
          ))}
        </div>

        <hr className="my-8" />

        <p className="text-[#336699] font-['trebuchet_ms,_arial,_helvetica']">*: corresponding author(s)</p>
      </div>
    </V1BaseLayout>
  );
}
