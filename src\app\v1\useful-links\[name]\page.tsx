import Link from 'next/link';
import { notFound } from 'next/navigation';

import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { linkSections } from '@/db/links';

interface PageProps {
  params: {
    name: string;
  };
}

export default function LinkDetailPage({ params }: PageProps) {
  const section = linkSections.find((s) => s.name === params.name);

  if (!section) {
    notFound();
  }

  return (
    <V1BaseLayout>
      <div className="max-w-6xl mx-auto p-4" style={{ color: '#336699' }}>
        {/* <h1 className="text-2xl font-bold mb-8 text-[#996633]">{section.title}</h1> */}

        {section.items.map((item, idx) => (
          <div key={idx} id={item.anchor.slice(1)} className="mb-8">
            <h2 className="text-[16px] text-[#336699] font-bold mb-2">
              <span className="font-['Arial,Helvetica'] italic font-bold">{item.text}</span>
            </h2>

            <div className="space-y-1">
              {item.links?.map((link, linkIdx) => (
                <div key={linkIdx} className="">
                  <a
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-[#669999] underline decoration-[#669999] visited:text-[#996633] visited:decoration-[#996633] hover:text-[#996633]"
                    style={{ fontFamily: 'Times New Roman,Times' }}
                  >
                    <b>{link.text}</b>
                  </a>
                </div>
              ))}
            </div>

            {/* {idx < section.items.length - 1 && (
              <div className="mt-4">
                <Link href="#top" className="text-[#003399]">
                  Back to Top
                </Link>
              </div>
            )} */}
          </div>
        ))}
        <div className="mt-4">
          <Link href="#top" className="text-[#996633] underline decoration-[#996633]">
            Back to Top
          </Link>
        </div>
      </div>
    </V1BaseLayout>
  );
}

export function generateStaticParams() {
  return linkSections.map((section) => ({
    name: section.name,
  }));
}
