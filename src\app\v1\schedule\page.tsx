import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { presentations } from '@/db/schedul';

export default function SchedulePage() {
  return (
    <V1BaseLayout>
      <div className="mb-16">
        {presentations.map((yearData) => (
          <div key={yearData.year} className="mb-8">
            <h3 className="text-2xl font-bold text-[#999900] mb-4">{yearData.year}&nbsp; Presentation</h3>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-[#6699CC] text-white">
                    <th className="border border-[#99CCCC] p-2 w-[9%]">No.</th>
                    <th className="border border-[#99CCCC] p-2 w-[47%]">Topic</th>
                    <th className="border border-[#99CCCC] p-2 w-[27%]">Speaker</th>
                    <th className="border border-[#99CCCC] p-2">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {yearData.presentations.map((presentation) => (
                    <tr key={presentation.id} className="hover:bg-[#f0f0f0]">
                      <td className="border border-[#99CCCC] p-2 text-center">{presentation.id}</td>
                      <td className="border border-[#99CCCC] p-2 text-center">{presentation.topic}</td>
                      <td className="border border-[#99CCCC] p-2 text-center">{presentation.speaker}</td>
                      <td className="border border-[#99CCCC] p-2 text-center">{presentation.date}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        ))}
      </div>
    </V1BaseLayout>
  );
}
