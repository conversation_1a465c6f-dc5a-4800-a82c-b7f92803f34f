'use client';

import Image from 'next/image';
import Link from 'next/link';

import { cn } from '@/lib/utils';

import type { Member } from '@/db/people';

interface MemberProfileProps {
  member: Member;
  type: 'current' | 'former';
}

export function MemberProfile({ member, type }: MemberProfileProps) {
  const memberId = member.name.toLowerCase().replace(/\s+/g, '-');
  // 生成简历链接的函数
  const getResumeLink = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-') // 替换非字母数字字符为连字符
      .replace(/-+/g, '-') // 替换多个连字符为单个
      .replace(/^-|-$/g, ''); // 移除首尾连字符
  };
  return (
    <div id={memberId} className="space-y-4 text-center scroll-mt-20">
      {/* 名字和标题 */}
      <h3 className="text-2xl font-bold font-arial text-[#003366]">{member.name}</h3>

      {/* 照片 */}
      <div className="flex justify-center">
        <div className="relative w-[400px] h-[300px]">
          {member.photo ? (
            <Image
              src={member.photo}
              alt={member.name}
              fill
              className="object-contain"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.src = '/images/placeholder.jpg';
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-100 flex items-center justify-center">
              <span className="text-gray-400">No Photo Available</span>
            </div>
          )}
        </div>
      </div>

      {/* 学历 */}
      {member.school && <p className="italic font-bold text-[#003366]">{member.school}</p>}

      {/* 信息表格 */}
      <table className="mx-auto w-[600px] border-0 cellspacing-0 text-left">
        <tbody>
          {/* 职位 */}
          {(member.title || member.currentPosition) && (
            <tr>
              <th style={{ width: '200px' }} className="font-bold text-[#000080]">
                {type === 'current' ? 'Job Title:' : 'Current Position:'}
              </th>
              <td style={{ width: '400px' }} className="text-[#336699]">
                {type === 'current' ? member.title : member.currentPosition}
              </td>
            </tr>
          )}

          {/* 研究兴趣 */}
          {member.researchInterests && (
            <tr>
              <th style={{ width: '200px' }} className="font-bold text-[#000080]">
                Research interests:
              </th>
              <td style={{ width: '400px' }} className="text-[#336699]">
                {member.researchInterests}
              </td>
            </tr>
          )}

          {/* 爱好 */}
          {member.hobbies && (
            <tr>
              <th style={{ width: '200px' }} className="font-bold text-[#000080]">
                Hobbies:
              </th>
              <td style={{ width: '400px' }} className="text-[#336699]">
                {member.hobbies}
              </td>
            </tr>
          )}

          {/* 办公室位置 */}
          {member.officeLocation && (
            <tr>
              <th style={{ width: '200px' }} className="font-bold text-[#000080]">
                Office Location:
              </th>
              <td style={{ width: '400px' }} className="text-[#336699]">
                {member.officeLocation}
              </td>
            </tr>
          )}

          {/* 邮箱 */}
          {member.email && (
            <tr>
              <th style={{ width: '200px' }} className="font-bold text-[#000080]">
                e-mail:
              </th>
              <td style={{ width: '400px' }}>
                <a href={`mailto:${member.email}`} className="text-[#669999] underline hover:underline">
                  {member.email}
                </a>
              </td>
            </tr>
          )}

          {/* 简历链接 */}
          {member.isResume && member.resume && (
            <tr>
              <td colSpan={2} className="text-left pt-4">
                <Link
                  href={`/v1/people/${getResumeLink(member.name)}`}
                  className="text-[#669999] underline hover:underline"
                >
                  Resume
                </Link>
              </td>
            </tr>
          )}
        </tbody>
      </table>

      {/* 返回顶部链接 */}
      <div className="text-left pt-4">
        <a href="#top" className="text-[#996633] underline decoration-[#996633] hover:underline">
          Back to Top
        </a>
      </div>
    </div>
  );
}
