'use client';

import Image from 'next/image';

export function GroupPhotos() {
  const photos = [
    {
      src: '',
      date: 'Nov, 2009',
      width: 600,
      height: 400,
    },
    {
      src: '',
      date: 'Jan, 2006',
      width: 600,
      height: 400,
    },
  ];

  return (
    <div className="space-y-8">
      <h3 className="text-center text-[#336699] text-xl font-bold font-arial">Group Photo</h3>
      {photos.map((photo, index) => (
        <div key={index} className="text-center space-y-2">
          <div className="relative w-full aspect-[3/2]">
            {photo.src ? (
              <Image
                src={photo.src}
                alt={`Group photo ${photo.date}`}
                fill
                className="object-contain"
                onError={(e) => {
                  const target = e.target as HTMLImageElement;
                  target.src = '/images/placeholder.jpg';
                }}
              />
            ) : (
              <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                <span className="text-gray-400">No Photo Available</span>
              </div>
            )}
          </div>
          <p className="text-[#336699]">{photo.date}</p>
        </div>
      ))}
    </div>
  );
}
