import Link from 'next/link';
import React from 'react';

import { currentMembers, formerMembers } from '@/db/people';

const MemberList = () => {
  // 获取成员ID的函数
  const getMemberId = (name: string) => name.toLowerCase().replace(/\s+/g, '-');
  return (
    <div className=" flex justify-center  mt-5 ">
      <div className="left-menu   ">
        <ul className="list-none text-right font-['Trebuchet_MS',Arial,Helvetica]">
          <li className="text-[#336699] font-bold  ">Members</li>
          {currentMembers.map((member) => (
            <li
              key={member.name}
              className="text-[#000080] underline decoration-[#999966] hover:underline cursor-pointer "
            >
              <Link
                href={`#${getMemberId(member.name)}`}
                className="text-[#000080] underline decoration-[#999966] hover:underline cursor-pointer"
              >
                {member.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>
      <div className="mx-12"></div>
      <div className="right-menu  ">
        <ul className="list-none font-['Trebuchet_MS',Arial,Helvetica]">
          <li className="text-[#336699] font-bold ">Former members</li>
          {formerMembers.map((member) => (
            <li
              key={member.name}
              className="text-[#000080] underline decoration-[#999966] hover:underline cursor-pointer "
            >
              <Link
                href={`#${getMemberId(member.name)}`}
                className="text-[#000080] underline decoration-[#999966] hover:underline cursor-pointer"
              >
                {member.name}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

export default MemberList;
