import Image from 'next/image';
import Link from 'next/link';

import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { images } from '@/constants';

export default function LodestarFaqPage() {
  return (
    <V1BaseLayout>
      <div className="max-w-6xl mx-auto p-4" style={{ color: '#336699' }}>
        {/* FAQ Content */}
        <div className="space-y-8">
          {/* Question 1 */}
          <div className="flex items-start gap-4">
            <Image src={images.Amaizbu1} alt="bullet" width={24} height={24} className="mt-1" />
            <div>
              <div className="flex gap-2">
                <span className="font-bold">Q:</span>
                <p className="text-justify">Why are output files wrong?</p>
              </div>

              <div className="ml-8 mt-4">
                <div className="flex gap-2">
                  <span className="font-bold">A:</span>
                  <p className="text-justify">
                    When multiple jobs are submitted through the JAVA interface, the response may be very slow. This may
                    lead to errors such as wrong output files being displayed. When you encounter such situation, you
                    should close the interface and restart LODESTAR interface.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </V1BaseLayout>
  );
}
