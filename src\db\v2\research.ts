// 研究数据 - 从原始 yangtze_web/home/<USER>
export interface ResearchTopic {
  id: number;
  title: string;
  content: string;
}

export interface ResearchData {
  topics: ResearchTopic[];
}

// 研究数据
export const researchData: ResearchData = {
  topics: [
    {
      id: 1,
      title: 'First Principles Methods for Open Quantum Systems and Application to Emerging Nanoelectronics',
      content:
        'Traditionally Quantum Chemistry deals the closed systems where energy and number of particles are fixed. With the development of materials science, nanotechnology and quantum computing, the needs for the accurate calculations of open systems are increasingly acute. A first-principle method has been developed to simulate the electronic dynamics of open systems. It follows the time evolution of reduced single electron density matrix, and has been employed to simulate the transient currents through molecular and nano-devices.',
    },
    {
      id: 2,
      title: 'Machine Learning and DFT',
      content:
        "The density-functional theory (DFT) method has become the most popular theoretical method in chemistry and material field due to its high computational efficiency and relative accuracy. Despite of the success, the quality of DFT calculations have room for improvement. In the pioneering work of our group, we have proposed to use neural networks to calibrate the standard heat of formation computed with DFT methods. We are currently employing other machine learning approaches to improve further DFT's accuracy or bypass DFT, reaching chemical accuracy.On the other hand, the accurate description of the exchange and correlation interaction between electrons is crucial to the DFT method. The exact exchange-correlation functional has been elusive, despite of the concerted efforts of its search over the last few decades. We are developing neural-network based schemes to improve existing exchange-correlation functionals and construct new functionals. These data-driven schemes rely on no physical approximation and are expected to yield the exact and universal exchange-correlation functional.",
    },
  ],
};
