'use client';

import Image from 'next/image';
import Link from 'next/link';
import { usePathname } from 'next/navigation';

interface HeaderProps {
  variant?: 'header1' | 'header2' | 'header3' | 'header4' | 'header5' | 'header6' | 'header7';
}

const V2Header = ({ variant = 'header1' }: HeaderProps) => {
  const pathname = usePathname();

  const navItems = [
    { href: '/v2', label: 'Home' },
    { href: '/v2/research', label: 'Research' },
    { href: '/v2/publications', label: 'Publications' },
    { href: '/v2/group', label: 'Group' },
    { href: '/v2/teaching', label: 'Teaching' },
    { href: '/v2/software', label: 'Software' },
    { href: '/v2/links', label: 'Links' },
  ];

  const isActive = (href: string) => {
    if (href === '/v2') {
      return pathname === '/v2';
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="relative w-full h-[360px] mx-auto max-w-[760px]">
      {/* Background image based on variant */}
      <div
        className="absolute inset-0 bg-no-repeat"
        style={{
          backgroundImage: `url('/v2/img/background/${variant}.jpg')`,
        }}
      />

      {/* Header content */}
      <div className="relative z-10 h-full flex flex-col">
        {/* Logo and title section */}
        <div className="flex items-start pt-6 pl-4">
          <Image src="/v2/img/background/logo.gif" alt="Logo" width={150} height={150} className="mr-4 float-left" />
          <div className="flex flex-col pt-6">
            <h1 className="text-[#671C04] text-[3.5em] font-bold leading-[75px] tracking-[-4px] m-0">
              GuanHua Chen Group
            </h1>
            <p className="text-[#C30] text-[1.8em] m-0 p-0">Theoretical and Computational Chemistry</p>
          </div>
        </div>

        {/* Navigation menu */}
        <nav className="absolute top-[295px] left-[72px] w-[616px]">
          <ul className="flex list-none m-0 p-0 text-[1.1em] overflow-hidden">
            {navItems.map((item) => (
              <li key={item.href} className="inline float-left">
                <Link
                  href={item.href}
                  className={`
                    block w-[88px] text-center no-underline lowercase
                    ${isActive(item.href) ? 'text-white' : 'text-[#FAAC58] hover:text-[#FF7F00]'}
                  `}
                >
                  {item.label}
                </Link>
              </li>
            ))}
          </ul>
        </nav>
      </div>
    </div>
  );
};

export default V2Header;
