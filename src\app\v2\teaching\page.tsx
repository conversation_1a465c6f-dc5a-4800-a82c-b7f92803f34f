import BaseLayout from '@/components/v2/layouts/BaseLayout';

export default function V2TeachingPage() {
  return (
    <BaseLayout headerType="header5" showSidebar={false} contentType="content5">
      <div style={{ width: '800px' }}>
        <h2>Level 2 courses</h2>
        <p>
          1. <b>CHEM2503 Intermediate Physical Chemistry</b> (
          <a href="http://yangtze.hku.hk/lecture/chem2503-06.ppt" className="text-[#FAAC58] hover:text-[#FF7F00]">
            notes
          </a>
          ) <br />
          2. <b>CHEM2541 Physical Chemistry I</b>
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a
            href="http://yangtze.hku.hk/lecture/chem2541-14-sec1-3.pptx"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PowerPoint Slides
          </a>{' '}
          updated on 15 Sep, 2014)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM2541_lecture1.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Lecture 1 notes
          </a>{' '}
          updated on 1 Sep, 2014)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM2541_lecture2.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Lecture 2 notes
          </a>{' '}
          updated on 12 Sep, 2014)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM2541_lecture3.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Lecture 3 notes
          </a>{' '}
          updated on 12 Sep, 2014)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM2541_lecture4.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Lecture 4 notes
          </a>{' '}
          updated on 12 Sep, 2014)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM2541_lecture5.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Lecture 5 notes
          </a>{' '}
          updated on 16 Sep, 2014)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM2541_lecture6.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Lecture 6 notes
          </a>{' '}
          updated on 23 Sep, 2014)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a
            href="http://yangtze.hku.hk/lecture/CHEM2541_Assignment1.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            Assignment 1
          </a>{' '}
          and{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM2541_Solution1.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            Solution
          </a>{' '}
          updated on 28 Jan, 2015)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(<span style={{ color: '#FFFF00' }}>Tutorial 1</span>:{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM2541_Tutorial1.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            Slides
          </a>
          ,{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM2541_Tutorial1_Question.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            Questions
          </a>{' '}
          and{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM2541_Tutorial1_Solution.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            Solution
          </a>{' '}
          updated on 29 Jan, 2015)
          <br />
          3. <b>CHEM3541 Physical Chemistry: Introduction to Quantum Chemistry</b>
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Lecture Notes{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_2019_Lecture_Notes.docx"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            DOCX
          </a>{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_2019_Lecture_Notes.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 29 Nov, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 1{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_1.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          deadline 19 Sep, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Selected Answers for Assignment 1{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_1_Answers.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 19 Sep, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 2{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_2.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          deadline 22 Oct, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Mathematical Backgrounds{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Mathematical_Backgrounds.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 17 Oct, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Checklist of Math Formulae{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Checklist_of_Mathematics_Formulae.docx"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            DOCX
          </a>{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Checklist_of_Mathematics_Formulae.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 17 Oct, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Answers for Assignment 2{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_2_Answers.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 31 Oct, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 3{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_3.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 5 Nov, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Solutions to Mid-Term Test{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Solutions_to_Midterm_Exam.docx"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            DOCX
          </a>{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Solutions_to_Midterm_Exam.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 5 Nov, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Assignment 4{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_4.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 27 Nov, 2019)
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Answers for Assignment 3 and 4{' '}
          <a
            href="http://yangtze.hku.hk/lecture/CHEM3541_Assignment_3_et_4_Answers.pdf"
            className="text-[#FAAC58] hover:text-[#FF7F00]"
          >
            PDF
          </a>{' '}
          updated on 27 Nov, 2019)
        </p>
        <br />

        <h2>Level 3 courses</h2>
        <p>
          1. <b>CHEM4543/6112 Advanced Physical Chemistry</b>
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM4543_2021.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Slides
          </a>{' '}
          updated on 13 Jan, 2021)
          <br />
          2. <b>CHEM4542/6109 Computational Chemistry</b>
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/CHEM4542_2021.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            Slides
          </a>{' '}
          updated on 13 Jan, 2021)
          <br />
          3. <b>CHEM3505 Electronic Spectroscopy</b> <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/chem3505_sep06.ppt" className="text-[#FAAC58] hover:text-[#FF7F00]">
            notes
          </a>
          ,{' '}
          <a href="http://yangtze.hku.hk/lecture/assignment.rar" className="text-[#FAAC58] hover:text-[#FF7F00]">
            assignments
          </a>
          )
          <br />
          4. <b>CHEM3542 Physical Chemistry: Statistical Thermodynamics and Kinetics Theory</b> <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(
          <a href="http://yangtze.hku.hk/lecture/chem3542-2015-16.ppt" className="text-[#FAAC58] hover:text-[#FF7F00]">
            notes
          </a>
          , Assignment 1 in{' '}
          <a href="Chem3542_Assignment_1-due_on_Feb05.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            PDF
          </a>
          /
          <a href="Chem3542_Assignment_1-due_on_Feb05.docx" className="text-[#FAAC58] hover:text-[#FF7F00]">
            MSWord
          </a>
          , Assignment 2 in{' '}
          <a href="Chem3542_Assignment_2.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            PDF
          </a>
          /
          <a href="Chem3542_Assignment_2.docx" className="text-[#FAAC58] hover:text-[#FF7F00]">
            MSWord
          </a>
          , Assignment 3 in{' '}
          <a href="Chem3542_Assignment_3.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            PDF
          </a>
          /
          <a href="Chem3542_Assignment_3.docx" className="text-[#FAAC58] hover:text-[#FF7F00]">
            MSWord
          </a>
          )
          <br />
          &nbsp;&nbsp;&nbsp;&nbsp;(Solutions for{' '}
          <a href="Solutions_to_Assignment_1.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            assignment 1
          </a>
          ,{' '}
          <a href="Solutions_to_Assignment_2.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            assignment 2
          </a>
          ,{' '}
          <a href="../lecture/chem3542_Assignment3_Solution.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            assignment 3
          </a>{' '}
          &{' '}
          <a href="../lecture/chem3542_Mid-Term_Solution.pdf" className="text-[#FAAC58] hover:text-[#FF7F00]">
            mid-term exam
          </a>
          )
          <br />
        </p>
        <br />

        <h2>Postgraduate courses</h2>
        <p>
          1. <b>Research Techniques in Chemistry</b> (
          <a href="http://yangtze.hku.hk/lecture/comput06-07.ppt" className="text-[#FAAC58] hover:text-[#FF7F00]">
            notes
          </a>
          ,{' '}
          <a href="http://yangtze.hku.hk/lecture/chemtech.doc" className="text-[#FAAC58] hover:text-[#FF7F00]">
            exercise
          </a>
          ,{' '}
          <a href="http://yangtze.hku.hk/lecture/1DEF.ent" className="text-[#FAAC58] hover:text-[#FF7F00]">
            download molecule
          </a>
          )
        </p>
        <br />

        <h2>M.Sc. courses</h2>
        <p>
          1. <b>Computational Modeling of Macromolecular Systems</b> (
          <a href="http://yangtze.hku.hk/lecture/comput-ms-04-05.ppt" className="text-[#FAAC58] hover:text-[#FF7F00]">
            notes
          </a>
          ,{' '}
          <a href="http://yangtze.hku.hk/lecture/1C51.ent" className="text-[#FAAC58] hover:text-[#FF7F00]">
            download molecule
          </a>
          )
        </p>
        <br />
      </div>
    </BaseLayout>
  );
}
