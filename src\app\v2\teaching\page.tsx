import BaseLayout from '@/components/v2/layouts/BaseLayout';
import { teachingData } from '@/db/v2/teaching';

import type { Course, TeachingResource } from '@/db/v2/teaching';

// 格式化文件类型显示
function formatFileType(format?: string): string {
  if (!format) return '';
  return format.toUpperCase();
}

// 渲染资源链接
function renderResourceLink(resource: TeachingResource): JSX.Element {
  const { title, url, format, updatedDate, deadline, type } = resource;

  let displayText = title;
  if (format) {
    displayText = `${title} ${formatFileType(format)}`;
  }

  let additionalInfo = '';
  if (updatedDate) {
    additionalInfo = ` updated on ${updatedDate}`;
  } else if (deadline) {
    additionalInfo = ` deadline ${deadline}`;
  }

  return (
    <a href={url} className="text-[#FAAC58] hover:text-[#FF7F00]">
      {displayText}
    </a>
  );
}

// 渲染课程资源
function renderCourseResources(course: Course): JSX.Element[] {
  const elements: JSX.Element[] = [];

  // 按类型分组资源
  const resourcesByType = course.resources.reduce(
    (acc, resource) => {
      if (!acc[resource.type]) {
        acc[resource.type] = [];
      }
      acc[resource.type]!.push(resource);
      return acc;
    },
    {} as Record<string, TeachingResource[]>
  );

  // 处理特殊的课程逻辑
  if (course.code === 'CHEM2541') {
    // CHEM2541 的特殊布局
    const slides = resourcesByType.slides || [];
    const notes = resourcesByType.notes || [];
    const assignments = resourcesByType.assignment || [];
    const solutions = resourcesByType.solution || [];
    const tutorials = resourcesByType.tutorial || [];

    // PowerPoint Slides
    if (slides.length > 0 && slides[0]) {
      elements.push(
        <span key="slides">
          ({renderResourceLink(slides[0])}
          {slides[0].updatedDate ? ` updated on ${slides[0].updatedDate}` : ''})
        </span>
      );
    }

    // Lecture notes
    notes.forEach((note, index) => {
      elements.push(
        <br key={`note-br-${index}`} />,
        <span key={`note-${index}`}>
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(note)}
          {note.updatedDate ? ` updated on ${note.updatedDate}` : ''})
        </span>
      );
    });

    // Assignment and Solution
    if (assignments.length > 0 && solutions.length > 0 && assignments[0] && solutions[0]) {
      elements.push(
        <br key="assignment-br" />,
        <span key="assignment">
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(assignments[0])} and {renderResourceLink(solutions[0])}
          {assignments[0].updatedDate ? ` updated on ${assignments[0].updatedDate}` : ''})
        </span>
      );
    }

    // Tutorial
    if (tutorials.length >= 3 && tutorials[0] && tutorials[1] && tutorials[2]) {
      elements.push(
        <br key="tutorial-br" />,
        <span key="tutorial">
          &nbsp;&nbsp;&nbsp;&nbsp;(<span style={{ color: '#FFFF00' }}>Tutorial 1</span>:{' '}
          {renderResourceLink(tutorials[0])}, {renderResourceLink(tutorials[1])} and {renderResourceLink(tutorials[2])}
          {tutorials[0].updatedDate ? ` updated on ${tutorials[0].updatedDate}` : ''})
        </span>
      );
    }
  } else if (course.code === 'CHEM3541') {
    // CHEM3541 的特殊布局
    const notes = resourcesByType.notes || [];
    const assignments = resourcesByType.assignment || [];
    const solutions = resourcesByType.solution || [];

    // Lecture Notes
    const lectureNotes = notes.filter((n) => n.title.includes('Lecture Notes'));
    if (lectureNotes.length >= 2 && lectureNotes[0] && lectureNotes[1]) {
      elements.push(
        <span key="lecture-notes">
          (Lecture Notes {renderResourceLink(lectureNotes[0])} {renderResourceLink(lectureNotes[1])}
          {lectureNotes[0].updatedDate ? ` updated on ${lectureNotes[0].updatedDate}` : ''})
        </span>
      );
    }

    // Assignments and solutions
    assignments.forEach((assignment, index) => {
      const relatedSolution = solutions.find((s) => s.title.includes(`Assignment ${index + 1}`));
      elements.push(
        <br key={`assignment-br-${index}`} />,
        <span key={`assignment-${index}`}>
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(assignment)}
          {assignment.deadline ? ` deadline ${assignment.deadline}` : ''})
        </span>
      );

      if (relatedSolution) {
        elements.push(
          <br key={`solution-br-${index}`} />,
          <span key={`solution-${index}`}>
            &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(relatedSolution)}
            {relatedSolution.updatedDate ? ` updated on ${relatedSolution.updatedDate}` : ''})
          </span>
        );
      }
    });

    // Other notes
    const otherNotes = notes.filter((n) => !n.title.includes('Lecture Notes'));
    otherNotes.forEach((note, index) => {
      elements.push(
        <br key={`other-note-br-${index}`} />,
        <span key={`other-note-${index}`}>
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(note)}
          {note.updatedDate ? ` updated on ${note.updatedDate}` : ''})
        </span>
      );
    });

    // Remaining solutions
    const otherSolutions = solutions.filter(
      (s) => !s.title.includes('Assignment 1') && !s.title.includes('Assignment 2')
    );
    otherSolutions.forEach((solution, index) => {
      elements.push(
        <br key={`other-solution-br-${index}`} />,
        <span key={`other-solution-${index}`}>
          &nbsp;&nbsp;&nbsp;&nbsp;({renderResourceLink(solution)}
          {solution.updatedDate ? ` updated on ${solution.updatedDate}` : ''})
        </span>
      );
    });
  } else if (course.code === 'CHEM3542') {
    // CHEM3542 的特殊布局
    const notes = resourcesByType.notes || [];
    const assignments = resourcesByType.assignment || [];
    const solutions = resourcesByType.solution || [];

    // Notes
    if (notes.length > 0 && notes[0]) {
      elements.push(<span key="notes">({renderResourceLink(notes[0])},</span>);
    }

    // Assignments
    assignments.forEach((assignment, index) => {
      const docxVersion = assignments.find((a) => a.title === assignment.title && a.format === 'docx');

      if (docxVersion && assignment.format === 'pdf') {
        elements.push(
          <span key={`assignment-${index}`}>
            {' '}
            Assignment {index + 1} in {renderResourceLink(assignment)}/{renderResourceLink(docxVersion)},
          </span>
        );
      }
    });

    elements.push(<span key="closing">)</span>);

    // Solutions
    if (solutions.length > 0) {
      elements.push(
        <br key="solutions-br" />,
        <span key="solutions">
          &nbsp;&nbsp;&nbsp;&nbsp;(Solutions for{' '}
          {solutions.map((solution, index) => (
            <span key={`solution-${index}`}>
              {index > 0 && ', '}
              {renderResourceLink(solution)}
            </span>
          ))}
          )
        </span>
      );
    }
  } else {
    // 默认布局 - 简单列出所有资源
    course.resources.forEach((resource, index) => {
      if (index > 0) {
        elements.push(<span key={`separator-${index}`}>, </span>);
      }
      elements.push(<span key={`resource-${index}`}>{renderResourceLink(resource)}</span>);
    });

    if (course.resources.length > 0) {
      elements.unshift(<span key="opening">(</span>);
      elements.push(<span key="closing">)</span>);
    }
  }

  return elements;
}

export default function V2TeachingPage() {
  return (
    <BaseLayout headerType="header5" showSidebar={false} contentType="content5">
      <div style={{ width: '800px' }}>
        <h2>Level 2 courses</h2>
        <p>
          {teachingData.level2Courses.map((course, index) => (
            <span key={course.id}>
              {index + 1}.{' '}
              <b>
                {course.code} {course.title}
              </b>{' '}
              {renderCourseResources(course)}
              <br />
            </span>
          ))}
        </p>
        <br />

        <h2>Level 3 courses</h2>
        <p>
          {teachingData.level3Courses.map((course, index) => (
            <span key={course.id}>
              {index + 1}.{' '}
              <b>
                {course.code} {course.title}
              </b>
              <br />
              &nbsp;&nbsp;&nbsp;&nbsp;{renderCourseResources(course)}
              <br />
            </span>
          ))}
        </p>
        <br />

        <h2>Postgraduate courses</h2>
        <p>
          {teachingData.postgraduateCourses.map((course, index) => (
            <span key={course.id}>
              {index + 1}. <b>{course.title}</b> {renderCourseResources(course)}
            </span>
          ))}
        </p>
        <br />

        <h2>M.Sc. courses</h2>
        <p>
          {teachingData.mscCourses.map((course, index) => (
            <span key={course.id}>
              {index + 1}. <b>{course.title}</b> {renderCourseResources(course)}
            </span>
          ))}
        </p>
        <br />
      </div>
    </BaseLayout>
  );
}
