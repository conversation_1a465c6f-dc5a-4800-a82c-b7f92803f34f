import Link from 'next/link';

interface FooterProps {
  visitorCount?: number;
}

const Footer = ({ visitorCount = 71041 }: FooterProps) => {
  return (
    <div className="footer">
      <p>
        You are the No. <b>{visitorCount}</b> visitor of our website since 29 Jul 2002
      </p>
      <p>
        copyright &copy;{' '}
        <Link href="/v2" className="text-[#FAAC58] hover:text-[#FF7F00]">
          GuanHua Chen Group
        </Link>{' '}
        2010 | web design by{' '}
        <Link href="/v2/group#NuoWang" className="text-[#FAAC58] hover:text-[#FF7F00]">
          <PERSON>uo Wang
        </Link>
      </p>
    </div>
  );
};

export default Footer;
