import { Metadata } from 'next';
import { headers } from 'next/headers';

import V1Footer from '@/components/v1/Footer';
import V1Header from '@/components/v1/Header';

export const metadata: Metadata = {
  title: `yangtze_web_nextjs - Classic Version`,
  description: `yangtze_web_nextjs - Classic Version`,
};

export default function V1Layout({ children }: { children: React.ReactNode }) {
  const headersList = headers();
  const hideLayout = headersList.get('x-hide-layout') === '1';

  return (
    <>
      {!hideLayout && <V1Header />}
      <main>{children}</main>
      {!hideLayout && <V1Footer />}
    </>
  );
}
