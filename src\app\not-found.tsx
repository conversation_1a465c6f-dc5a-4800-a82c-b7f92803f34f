import Link from 'next/link';

export default function NotFound() {
  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-md mx-auto text-center p-8">
        <h2 className="text-3xl font-bold text-gray-800 mb-4">Page Not Found</h2>
        <p className="text-gray-600 mb-6">Could not find the requested page.</p>

        <div className="space-y-4">
          <Link
            href="/"
            className="block bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
          >
            Choose Version
          </Link>

          <div className="flex gap-4 justify-center">
            <Link href="/v1/home" className="text-blue-600 hover:text-blue-800 underline">
              V1 Home
            </Link>
            <Link href="/v2/home" className="text-purple-600 hover:text-purple-800 underline">
              V2 Home
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
