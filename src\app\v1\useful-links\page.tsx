'use client';

import Image from 'next/image';
import Link from 'next/link';

import V1BaseLayout from '@/components/v1/layouts/BaseLayout';
import { images } from '@/constants';
import { linkSections } from '@/db/links';

export default function UsefulLinksPage() {
  const handleDirectLink = (url: string) => {
    window.location.href = url;
  };

  return (
    <V1BaseLayout>
      <div className="max-w-6xl mx-auto p-8" style={{ color: '#336699' }}>
        {linkSections.map((section, idx) => (
          <div key={idx} className="mb-8">
            {/* 标题部分 */}
            <Link
              href={`/useful-links/${section.name}`}
              className="text-[#999966] underline decoration-[#999966] font-bold italic block mb-4"
            >
              {idx + 1}. {section.title}
            </Link>
            <ul>
              {section.items.map((item, itemIdx) => (
                <li key={itemIdx} className="flex items-center gap-4 mb-2">
                  <Image src={images.Amaizbu1} alt="bullet" width={15} height={15} className="ml-[13px]" unoptimized />
                  {/* 修改条件判断和类型检查 */}
                  {item.links && item.links.length === 1 ? (
                    <button
                      onClick={() => {
                        // 在这里再次检查确保 links 存在
                        if (item.links && item.links[0]) {
                          handleDirectLink(item.links[0].url);
                        }
                      }}
                      className="text-[#669999] underline decoration-[#999966] font-bold italic text-left"
                    >
                      {item.text}
                    </button>
                  ) : (
                    <Link
                      href={`/useful-links/${section.name}${item.anchor}`}
                      className="text-[#999966] underline decoration-[#999966] visited:text-[#669999] visited:decoration-[#669999] font-bold italic"
                    >
                      {item.text}
                    </Link>
                  )}
                </li>
              ))}
            </ul>
          </div>
        ))}

        <div className="flex items-start justify-start mt-4">
          <a href="#" className="text-[#669999] underline visited:text-[#999966] decoration-[#669999]">
            Back to Top
          </a>
        </div>
      </div>
    </V1BaseLayout>
  );
}
